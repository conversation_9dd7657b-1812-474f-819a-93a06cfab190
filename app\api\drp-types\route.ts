import { NextResponse } from "next/server"
import { supabase } from "@/lib/supabase"

// GET /api/drp-types -> returns all DRP types ordered by name
export async function GET() {
  const { data, error } = await supabase.from("drp_types").select("*").order("name")

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json(data, { status: 200 })
}

// POST /api/drp-types -> create new DRP type { name: string }
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { name } = body as { name?: string }

    if (!name || typeof name !== "string" || name.trim() === "") {
      return NextResponse.json({ error: "Name is required" }, { status: 400 })
    }

    const { data, error } = await supabase.from("drp_types").insert([{ name: name.trim() }]).select().single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (err: any) {
    return NextResponse.json({ error: err.message || "Unexpected error" }, { status: 500 })
  }
} 