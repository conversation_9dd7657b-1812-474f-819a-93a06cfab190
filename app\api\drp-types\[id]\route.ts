import { NextResponse } from "next/server"
import { supabase } from "@/lib/supabase"

interface Params {
  params: { id: string }
}

// PUT /api/drp-types/[id] -> update name
export async function PUT(request: Request, { params }: Params) {
  const id = Number(params.id)
  if (Number.isNaN(id)) {
    return NextResponse.json({ error: "Invalid id" }, { status: 400 })
  }

  try {
    const body = await request.json()
    const { name } = body as { name?: string }

    if (!name || typeof name !== "string" || name.trim() === "") {
      return NextResponse.json({ error: "Name is required" }, { status: 400 })
    }

    const { data, error } = await supabase.from("drp_types").update({ name: name.trim() }).eq("id", id).select().single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 200 })
  } catch (err: any) {
    return NextResponse.json({ error: err.message || "Unexpected error" }, { status: 500 })
  }
}

export const PATCH = PUT

// DELETE /api/drp-types/[id]
export async function DELETE(_request: Request, { params }: Params) {
  const id = Number(params.id)
  if (Number.isNaN(id)) {
    return NextResponse.json({ error: "Invalid id" }, { status: 400 })
  }

  const { error } = await supabase.from("drp_types").delete().eq("id", id)

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({ message: "Deleted" }, { status: 200 })
} 