import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}

/**
 * Hook for managing tab order and focus management in forms
 * Provides utilities for setting proper tab indices and handling focus flow
 */
export function useTabOrder() {
  const [tabIndex, setTabIndex] = React.useState(1)

  const getNextTabIndex = React.useCallback(() => {
    const current = tabIndex
    setTabIndex(prev => prev + 1)
    return current
  }, [tabIndex])

  const resetTabOrder = React.useCallback(() => {
    setTabIndex(1)
  }, [])

  return {
    getNextTabIndex,
    resetTabOrder,
    currentTabIndex: tabIndex
  }
}

/**
 * Hook for auto-focusing the first form element on page load
 * Supports both initial page loads and client-side navigation
 */
export function useAutoFocus(enabled: boolean = true) {
  const firstInputRef = React.useRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null>(null)

  React.useEffect(() => {
    if (enabled && firstInputRef.current) {
      // Small delay to ensure the component is fully rendered
      const timer = setTimeout(() => {
        firstInputRef.current?.focus()
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [enabled])

  return firstInputRef
}
