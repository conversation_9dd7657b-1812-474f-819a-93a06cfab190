import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"
import Link from "next/link"
import { Home } from "lucide-react"
import <PERSON>Nav from "@/components/main-nav"
import { Toaster } from "@/components/ui/toaster"
import { getServerSession } from "next-auth/next"
import AuthSessionProvider from "@/components/session-provider"
import { authOptions } from "@/lib/auth"

export const metadata: Metadata = {
  title: "Clinical Dashboard",
  description: "Created by <PERSON><PERSON><PERSON>",
  generator: "<3",
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const session = await getServerSession(authOptions)
  return (
    <html lang="en">
      <body>
        <AuthSessionProvider session={session}>
          <header className="border-b bg-background">
            <div className="container flex h-14 items-center px-4">
              <Link href="/" className="flex items-center gap-2 font-semibold">
                <Home className="h-5 w-5" />
                <span>Clinical Dashboard</span>
              </Link>
              <MainNav />
            </div>
          </header>
          {children}
          <Toaster />
        </AuthSessionProvider>
      </body>
    </html>
  )
}
