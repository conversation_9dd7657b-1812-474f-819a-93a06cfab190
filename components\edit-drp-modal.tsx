"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { supabase, type DrpEntry, type Medication, type DrpType } from "@/lib/supabase"
import { toast } from "@/hooks/use-toast"

interface EditDrpModalProps {
  entry: DrpEntry
  onClose: () => void
  onSuccess: () => void
}

// DRP types will be loaded dynamically from database
const DOCTOR_RESPONSES = ["Accepted", "Rejected", "No Response"]

const PHARMACISTS = ["Dr.<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>.<PERSON><PERSON>", "<PERSON>.<PERSON><PERSON>", "<PERSON>.<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]

export function EditDrpModal({ entry, onClose, onSuccess }: EditDrpModalProps) {
  const [formData, setFormData] = useState({
    patientId: entry.patient_id,
    patientName: entry.patient_name,
    medication: entry.medication,
    drpType: entry.drp_type,
    doctorResponse: entry.doctor_response,
    pharmacistSignature: entry.pharmacist_signature,
  })

  const [medications, setMedications] = useState<Medication[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [drpTypes, setDrpTypes] = useState<DrpType[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    fetchMedications()
    fetchDrpTypes()
  }, [])

  const fetchMedications = async () => {
    try {
      const { data, error } = await supabase.from("medications").select("*").order("name")

      if (error) throw error
      setMedications(data || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load medications",
        variant: "destructive",
      })
    }
  }

  const fetchDrpTypes = async () => {
    try {
      const { data, error } = await supabase.from("drp_types").select("*").order("name")
      if (error) throw error
      setDrpTypes(data || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load DRP types",
        variant: "destructive",
      })
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.patientId) {
      newErrors.patientId = "Patient ID is required"
    } else if (!/^\d+$/.test(formData.patientId)) {
      newErrors.patientId = "Patient ID must contain only numbers"
    }

    if (!formData.patientName.trim()) {
      newErrors.patientName = "Patient name is required"
    }

    if (!formData.medication) {
      newErrors.medication = "Medication selection is required"
    }

    if (!formData.drpType) {
      newErrors.drpType = "DRP type selection is required"
    }

    if (!formData.doctorResponse) {
      newErrors.doctorResponse = "Doctor response is required"
    }

    if (!formData.pharmacistSignature) {
      newErrors.pharmacistSignature = "Pharmacist signature is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      const { error } = await supabase
        .from("drp_entries")
        .update({
          patient_id: formData.patientId,
          patient_name: formData.patientName,
          medication: formData.medication,
          drp_type: formData.drpType,
          doctor_response: formData.doctorResponse,
          pharmacist_signature: formData.pharmacistSignature,
        })
        .eq("id", entry.id)

      if (error) throw error

      toast({
        title: "Success",
        description: "DRP entry updated successfully",
      })

      onSuccess()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update DRP entry",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit DRP Entry</DialogTitle>
          <DialogDescription>Make changes to the DRP entry. Click save when you're done.</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-patientId">Patient ID</Label>
              <Input
                id="edit-patientId"
                placeholder="Enter Patient ID (numbers only)"
                value={formData.patientId}
                onChange={(e) => setFormData((prev) => ({ ...prev, patientId: e.target.value }))}
                className={errors.patientId ? "border-red-500" : ""}
              />
              {errors.patientId && <p className="text-sm text-red-500">{errors.patientId}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-patientName">Patient Name</Label>
              <Input
                id="edit-patientName"
                placeholder="Enter Patient Name"
                value={formData.patientName}
                onChange={(e) => setFormData((prev) => ({ ...prev, patientName: e.target.value }))}
                className={errors.patientName ? "border-red-500" : ""}
              />
              {errors.patientName && <p className="text-sm text-red-500">{errors.patientName}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Medication</Label>
            <Select
              value={formData.medication}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, medication: value }))}
            >
              <SelectTrigger className={errors.medication ? "border-red-500" : ""}>
                <SelectValue placeholder="Select medication" />
              </SelectTrigger>
              <SelectContent>
                {medications.map((med) => (
                  <SelectItem key={med.id} value={med.name}>
                    {med.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.medication && <p className="text-sm text-red-500">{errors.medication}</p>}
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>DRP Type</Label>
              <Select
                value={formData.drpType}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, drpType: value }))}
              >
                <SelectTrigger className={errors.drpType ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select DRP type" />
                </SelectTrigger>
                <SelectContent>
                  {drpTypes.map((type) => (
                    <SelectItem key={type.id} value={type.name}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.drpType && <p className="text-sm text-red-500">{errors.drpType}</p>}
            </div>

            <div className="space-y-2">
              <Label>Doctor Response</Label>
              <Select
                value={formData.doctorResponse}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, doctorResponse: value }))}
              >
                <SelectTrigger className={errors.doctorResponse ? "border-red-500" : ""}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {DOCTOR_RESPONSES.map((response) => (
                    <SelectItem key={response} value={response}>
                      {response}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.doctorResponse && <p className="text-sm text-red-500">{errors.doctorResponse}</p>}
            </div>

            <div className="space-y-2">
              <Label>Pharmacist Signature</Label>
              <Select
                value={formData.pharmacistSignature}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, pharmacistSignature: value }))}
              >
                <SelectTrigger className={errors.pharmacistSignature ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select pharmacist" />
                </SelectTrigger>
                <SelectContent>
                  {PHARMACISTS.map((pharmacist) => (
                    <SelectItem key={pharmacist} value={pharmacist}>
                      {pharmacist}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.pharmacistSignature && <p className="text-sm text-red-500">{errors.pharmacistSignature}</p>}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
