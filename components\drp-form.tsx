"use client"

import type React from "react"

import { useState, useEffect, useMemo } from "react"
import { useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus } from "lucide-react"
import { supabase, type Medication, type DrpType } from "@/lib/supabase"
import Fuse from "fuse.js"
import { toast } from "@/hooks/use-toast"

interface DrpFormProps {
  onSubmitSuccess: () => void
}

// Dynamic DRP types will be fetched from database
const DOCTOR_RESPONSES = ["Accepted", "Rejected", "No Response"]

export function DrpForm({ onSubmitSuccess }: DrpFormProps) {
  const { data: session } = useSession()
  const [formData, setFormData] = useState({
    patientId: "",
    patientName: "",
    medication: "",
    drpType: "",
    doctorResponse: "No Response",
    pharmacistSignature: session?.user?.name ?? "",
  })

  const [medications, setMedications] = useState<Medication[]>([])
  const [newMedication, setNewMedication] = useState("")
  const [drpTypes, setDrpTypes] = useState<DrpType[]>([])
  // Local state for search term & results visibility
  const [searchTerm, setSearchTerm] = useState("")
  const [showResults, setShowResults] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    fetchMedications()
    fetchDrpTypes()
  }, [])

  // Keep pharmacistSignature in sync with session changes
  useEffect(() => {
    if (session?.user?.name) {
      setFormData((prev) => ({ ...prev, pharmacistSignature: session.user.name }))
    }
  }, [session])

  const fetchMedications = async () => {
    try {
      const { data, error } = await supabase.from("medications").select("*").order("name")

      if (error) throw error
      setMedications(data || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load medications",
        variant: "destructive",
      })
    }
  }

  const fetchDrpTypes = async () => {
    try {
      const { data, error } = await supabase.from("drp_types").select("*").order("name")
      if (error) throw error
      setDrpTypes(data || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load DRP types",
        variant: "destructive",
      })
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.patientId) {
      newErrors.patientId = "Patient ID is required"
    } else if (!/^\d+$/.test(formData.patientId)) {
      newErrors.patientId = "Patient ID must contain only numbers"
    }

    if (!formData.patientName.trim()) {
      newErrors.patientName = "Patient name is required"
    }

    if (!formData.medication) {
      newErrors.medication = "Medication selection is required"
    }

    if (!formData.drpType) {
      newErrors.drpType = "DRP type selection is required"
    }

    if (!formData.doctorResponse) {
      newErrors.doctorResponse = "Doctor response is required"
    }

    // pharmacistSignature is auto-populated from session, no manual validation needed

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isFormValid = () => {
    return (
      formData.patientId.trim() !== "" &&
      /^\d+$/.test(formData.patientId) &&
      formData.patientName.trim() !== "" &&
      formData.medication !== "" &&
      formData.drpType !== "" &&
      formData.doctorResponse !== "" &&
      formData.pharmacistSignature !== ""
    )
  }

  const handleAddMedication = async () => {
    if (!newMedication.trim()) return

    try {
      const { data, error } = await supabase
        .from("medications")
        .insert([{ name: newMedication.trim() }])
        .select()

      if (error) throw error

      await fetchMedications()
      setNewMedication("")
      toast({
        title: "Success",
        description: "Medication added successfully",
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add medication",
        variant: "destructive",
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      const { error } = await supabase.from("drp_entries").insert([
        {
          patient_id: formData.patientId,
          patient_name: formData.patientName,
          medication: formData.medication,
          drp_type: formData.drpType,
          doctor_response: formData.doctorResponse,
          pharmacist_signature: formData.pharmacistSignature,
          username: (session?.user as any)?.username,
        },
      ])

      if (error) throw error

      // Partially clear the form for the next entry, preserving patient and pharmacist info
      setFormData(prevData => ({
        ...prevData,
        medication: "",
        drpType: "",
        doctorResponse: "No Response",
      }))
      setErrors({})

      toast({
        title: "Success",
        description: "DRP Entry Submitted Successfully",
      })

      onSubmitSuccess()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to submit DRP entry",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClearForm = () => {
    setFormData({
      patientId: "",
      patientName: "",
      medication: "",
      drpType: "",
      doctorResponse: "No Response",
      pharmacistSignature: session?.user?.name ?? "",
    })
    setSearchTerm("")
    setShowResults(false)
    setErrors({})
  }

  const formIsValid = isFormValid()

  // Memoized Fuse instance for performant fuzzy searching
  const fuse = useMemo(() => {
    return new Fuse(medications, {
      keys: ["name"],
      threshold: 0.4, // balance between strict & loose matching
      ignoreLocation: true,
      minMatchCharLength: 2,
    })
  }, [medications])

  // Derived search results whenever searchTerm changes
  const searchResults: Medication[] = useMemo(() => {
    if (searchTerm.length < 3) return []
    // Fuse returns objects with an `item` property containing the original object
    return fuse.search(searchTerm).map((res: any) => res.item as Medication)
  }, [searchTerm, fuse])

  return (
    <Card className="w-full mx-auto">
      <CardHeader>
        <CardTitle>Submit DRP Entry</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Patient Information Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="patientId">Patient ID</Label>
              <Input
                id="patientId"
                placeholder="Enter Patient ID (numbers only)"
                value={formData.patientId}
                onChange={(e) => setFormData((prev) => ({ ...prev, patientId: e.target.value }))}
                inputMode="numeric"
                pattern="[0-9]*"
                className={errors.patientId ? "border-red-500" : ""}
              />
              {errors.patientId && <p className="text-sm text-red-500">{errors.patientId}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="patientName">Patient Name</Label>
              <Input
                id="patientName"
                placeholder="Enter Patient Name"
                value={formData.patientName}
                onChange={(e) => setFormData((prev) => ({ ...prev, patientName: e.target.value }))}
                className={errors.patientName ? "border-red-500" : ""}
              />
              {errors.patientName && <p className="text-sm text-red-500">{errors.patientName}</p>}
            </div>

            <div className="space-y-2">
              <Label>Pharmacist</Label>
              <Input value={session?.user?.name ?? ""} readOnly />
            </div>
          </div>

          {/* Medication & Add-New Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Medication Search */}
            <div className="space-y-2 relative">
              <Label>Medication</Label>
              <Input
                placeholder="Search medication"
                value={searchTerm}
                onChange={(e) => {
                  const val = e.target.value
                  setSearchTerm(val)
                  setFormData((prev) => ({ ...prev, medication: val }))
                  setShowResults(val.length >= 3)
                }}
                onFocus={() => setShowResults(searchTerm.length >= 3)}
                className={errors.medication ? "border-red-500" : ""}
              />

              {/* Dynamic Search Results */}
              {showResults && (
                <ul className="absolute z-20 mt-1 w-full max-h-60 overflow-y-auto rounded-md border bg-white shadow-lg">
                  {searchResults.length > 0 ? (
                    searchResults.map((med: Medication) => (
                      <li
                        key={med.id}
                        className="cursor-pointer px-3 py-2 hover:bg-gray-100"
                        onClick={() => {
                          setSearchTerm(med.name)
                          setFormData((prev) => ({ ...prev, medication: med.name }))
                          setShowResults(false)
                        }}
                      >
                        {med.name}
                      </li>
                    ))
                  ) : (
                    <li className="px-3 py-2 text-sm text-gray-500">No medications found.</li>
                  )}
                </ul>
              )}

              {errors.medication && <p className="text-sm text-red-500">{errors.medication}</p>}
            </div>

            {/* Add new medication */}
            <div className="space-y-2">
              <Label className="invisible lg:visible">&nbsp;</Label>
              <div className="flex gap-1 mt-0 lg:mt-6">
                <Input
                  placeholder="New medication"
                  value={newMedication}
                  onChange={(e) => setNewMedication(e.target.value)}
                  className="flex-1"
                />
                <Button type="button" onClick={handleAddMedication} size="icon" variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* DRP Details Row - DRP Type and Doctor Response side by side */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>DRP Type</Label>
              <Select
                value={formData.drpType}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, drpType: value }))}
              >
                <SelectTrigger className={errors.drpType ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select DRP type" />
                </SelectTrigger>
                <SelectContent>
                  {drpTypes.map((type) => (
                    <SelectItem key={type.id} value={type.name}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.drpType && <p className="text-sm text-red-500">{errors.drpType}</p>}
            </div>

            <div className="space-y-2">
              <Label>Doctor Response</Label>
              <Select
                value={formData.doctorResponse}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, doctorResponse: value }))}
              >
                <SelectTrigger className={errors.doctorResponse ? "border-red-500" : ""}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {DOCTOR_RESPONSES.map((response) => (
                    <SelectItem key={response} value={response}>
                      {response}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.doctorResponse && <p className="text-sm text-red-500">{errors.doctorResponse}</p>}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button type="submit" className="flex-1" disabled={!formIsValid || isSubmitting}>
              {isSubmitting ? "Submitting..." : "Submit DRP Entry"}
            </Button>
            <Button type="button" variant="outline" onClick={handleClearForm} disabled={isSubmitting}>
              Clear Form
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default DrpForm
