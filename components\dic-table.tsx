"use client"

import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Edit, Trash2, Search, Eye, ChevronDown, ChevronRight } from "lucide-react"
import { supabase } from "@/lib/supabase"
import { useSession } from "next-auth/react"
import { toast } from "@/hooks/use-toast"
import { useIsMobile } from "@/hooks/use-mobile"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface DicTableProps {
  refreshTrigger: number
}

type DicEntry = {
  id: number
  question: string
  answer: string
  inquirer: string
  pharmacist_signature: string
  username: string
  created_at: string
  updated_at: string
}

export function DicTable({ refreshTrigger }: DicTableProps) {
  const { data: session } = useSession()
  const [entries, setEntries] = useState<DicEntry[]>([])
  const [filteredEntries, setFilteredEntries] = useState<DicEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterPharmacist, setFilterPharmacist] = useState("all")
  // Dynamic list of pharmacists fetched from users table
  const [pharmacists, setPharmacists] = useState<string[]>([])
  const [editingEntry, setEditingEntry] = useState<DicEntry | null>(null)
  const [viewingEntry, setViewingEntry] = useState<DicEntry | null>(null)
  const [editFormData, setEditFormData] = useState({
    question: "",
    answer: "",
    inquirer: "",
    pharmacistSignature: "",
  })
  const isMobile = useIsMobile()

  useEffect(() => {
    fetchEntries()
  }, [refreshTrigger])

  // Fetch pharmacist names for dropdown
  useEffect(() => {
    const fetchPharmacists = async () => {
      const { data, error } = await supabase.from("users").select("name")
      if (!error && data) {
        setPharmacists(data.map((u: { name: string }) => u.name))
      }
    }
    fetchPharmacists()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [entries, searchTerm, filterPharmacist])

  const fetchEntries = async () => {
    try {
      const { data, error } = await supabase.from("dic_entries").select("*").order("created_at", { ascending: false })

      if (error) throw error
      setEntries(data || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load DIC entries",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = entries

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (entry) =>
          entry.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
          entry.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
          entry.inquirer.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    // Pharmacist filter
    if (filterPharmacist !== "all") {
      filtered = filtered.filter((entry) => entry.pharmacist_signature === filterPharmacist)
    }

    setFilteredEntries(filtered)
  }

  const handleDelete = async (id: number) => {
    try {
      const { error } = await supabase.from("dic_entries").delete().eq("id", id)

      if (error) throw error

      toast({
        title: "Success",
        description: "DIC entry deleted successfully",
      })

      fetchEntries()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete entry",
        variant: "destructive",
      })
    }
  }

  const handleEdit = (entry: DicEntry) => {
    setEditingEntry(entry)
    setEditFormData({
      question: entry.question,
      answer: entry.answer,
      inquirer: entry.inquirer,
      pharmacistSignature: entry.pharmacist_signature,
    })
  }

  const handleUpdateEntry = async () => {
    if (!editingEntry) return

    try {
      const { error } = await supabase
        .from("dic_entries")
        .update({
          question: editFormData.question,
          answer: editFormData.answer,
          inquirer: editFormData.inquirer,
          pharmacist_signature: editFormData.pharmacistSignature,
          updated_at: new Date().toISOString(),
        })
        .eq("id", editingEntry.id)

      if (error) throw error

      toast({
        title: "Success",
        description: "DIC entry updated successfully",
      })

      setEditingEntry(null)
      fetchEntries()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update entry",
        variant: "destructive",
      })
    }
  }

  // Replace derived pharmacist list with dynamic list from users table
  const uniquePharmacists = pharmacists

  if (loading) {
    return <div className="text-center py-8">Loading...</div>
  }

  // Mobile card view component
  const MobileEntryCard = ({ entry }: { entry: DicEntry }) => (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{entry.question}</CardTitle>
      </CardHeader>
      <CardContent className="py-2">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="details" className="border-none">
            <AccordionTrigger className="py-1 text-sm">View Details</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-3 text-sm">
                <div>
                  <Label className="text-xs text-muted-foreground">Answer</Label>
                  <p className="mt-1 whitespace-pre-wrap">{entry.answer}</p>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs text-muted-foreground">Inquirer</Label>
                    <p className="mt-1">{entry.inquirer}</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Pharmacist</Label>
                    <p className="mt-1">{entry.pharmacist_signature}</p>
                  </div>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Date</Label>
                  <p className="mt-1">{new Date(entry.created_at).toLocaleDateString()}</p>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
      <CardFooter className="pt-0 pb-4 flex justify-end gap-2">
        <Button variant="outline" size="sm" onClick={() => setViewingEntry(entry)}>
          <Eye className="h-4 w-4" />
        </Button>

        {session?.user && (session.user.role === "admin" || (session.user as any).username === entry.username) && (
          <>
            <Button variant="outline" size="sm" onClick={() => handleEdit(entry)}>
              <Edit className="h-4 w-4" />
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete the DIC entry.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={() => handleDelete(entry.id)}>Delete</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </>
        )}
      </CardFooter>
    </Card>
  )

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Drug Information Center Entries</CardTitle>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search questions, answers, or inquirers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex gap-2">
            <Select value={filterPharmacist} onValueChange={setFilterPharmacist}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by Pharmacist" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Pharmacists</SelectItem>
                {uniquePharmacists.map((pharmacist) => (
                  <SelectItem key={pharmacist} value={pharmacist}>
                    {pharmacist}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {isMobile ? (
          // Mobile view - Card layout
          <div>
            {filteredEntries.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No DIC entries found</div>
            ) : (
              filteredEntries.map((entry) => (
                <MobileEntryCard key={entry.id} entry={entry} />
              ))
            )}
          </div>
        ) : (
          // Desktop view - Table layout
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Question</TableHead>
                  <TableHead>Answer</TableHead>
                  <TableHead>Inquirer</TableHead>
                  <TableHead>Pharmacist</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEntries.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      No DIC entries found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell className="max-w-xs">
                        <div className="truncate font-medium">{entry.question}</div>
                      </TableCell>
                      <TableCell className="max-w-xs">
                        <div className="truncate">{entry.answer}</div>
                      </TableCell>
                      <TableCell>{entry.inquirer}</TableCell>
                      <TableCell>{entry.pharmacist_signature}</TableCell>
                      <TableCell>{new Date(entry.created_at).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" onClick={() => setViewingEntry(entry)}>
                            <Eye className="h-4 w-4" />
                          </Button>

                          {session?.user && (session.user.role === "admin" || (session.user as any).username === entry.username) && (
                            <>
                              <Button variant="outline" size="sm" onClick={() => handleEdit(entry)}>
                                <Edit className="h-4 w-4" />
                              </Button>

                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button variant="outline" size="sm">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      This action cannot be undone. This will permanently delete the DIC entry.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => handleDelete(entry.id)}>Delete</AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}

        {filteredEntries.length > 0 && (
          <div className="mt-4 text-sm text-muted-foreground">
            Showing {filteredEntries.length} of {entries.length} entries
          </div>
        )}
      </CardContent>

      {/* View Dialog */}
      <Dialog open={!!viewingEntry} onOpenChange={(open) => !open && setViewingEntry(null)}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>View DIC Entry</DialogTitle>
            <DialogDescription>Full details of the DIC entry.</DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>Question</Label>
              <p className="whitespace-pre-wrap border rounded-md p-2 bg-muted">
                {viewingEntry?.question}
              </p>
            </div>

            <div className="space-y-2">
              <Label>Answer</Label>
              <p className="whitespace-pre-wrap border rounded-md p-2 bg-muted">
                {viewingEntry?.answer}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Inquirer</Label>
                <p className="border rounded-md p-2 bg-muted">{viewingEntry?.inquirer}</p>
              </div>

              <div className="space-y-2">
                <Label>Pharmacist Signature</Label>
                <p className="border rounded-md p-2 bg-muted">{viewingEntry?.pharmacist_signature}</p>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label>Date</Label>
                <p className="border rounded-md p-2 bg-muted">
                  {viewingEntry ? new Date(viewingEntry.created_at).toLocaleString() : ""}
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => setViewingEntry(null)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={!!editingEntry} onOpenChange={(open) => !open && setEditingEntry(null)}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit DIC Entry</DialogTitle>
            <DialogDescription>Make changes to the DIC entry below.</DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-question">Question</Label>
              <Textarea
                id="edit-question"
                value={editFormData.question}
                onChange={(e) => setEditFormData((prev) => ({ ...prev, question: e.target.value }))}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-answer">Answer</Label>
              <Textarea
                id="edit-answer"
                value={editFormData.answer}
                onChange={(e) => setEditFormData((prev) => ({ ...prev, answer: e.target.value }))}
                rows={6}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-inquirer">Inquirer</Label>
                <Input
                  id="edit-inquirer"
                  value={editFormData.inquirer}
                  onChange={(e) => setEditFormData((prev) => ({ ...prev, inquirer: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Pharmacist Signature</Label>
                <Select
                  value={editFormData.pharmacistSignature}
                  onValueChange={(value) => setEditFormData((prev) => ({ ...prev, pharmacistSignature: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select pharmacist" />
                  </SelectTrigger>
                  <SelectContent>
                    {uniquePharmacists.map((pharmacist) => (
                      <SelectItem key={pharmacist} value={pharmacist}>
                        {pharmacist}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditingEntry(null)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateEntry}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
