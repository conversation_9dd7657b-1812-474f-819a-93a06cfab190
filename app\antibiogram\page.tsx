"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { AntibiogramForm } from "@/components/antibiogram-form"
import { AntibiogramTable } from "@/components/antibiogram-table"

export default function AntibiogramPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleSubmitSuccess = () => {
    setRefreshTrigger((prev) => prev + 1)
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8 text-center">
        <h1 className="text-2xl font-bold text-gray-900">Antibiogram Management</h1>
        <p className="text-gray-600 mt-2">Submit and manage antibiogram records</p>
      </div>

      <Tabs defaultValue="submit" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="submit">Submit Antibiogram</TabsTrigger>
          <TabsTrigger value="entries">View Antibiograms</TabsTrigger>
        </TabsList>

        <TabsContent value="submit" className="mt-6 space-y-6">
          <AntibiogramForm onSubmitSuccess={handleSubmitSuccess} />
        </TabsContent>

        <TabsContent value="entries" className="mt-6 space-y-6">
          <AntibiogramTable refreshTrigger={refreshTrigger} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
