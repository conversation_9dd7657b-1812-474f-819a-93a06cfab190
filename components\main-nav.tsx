"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"
import { Menu } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>rigger, Sheet<PERSON>ontent, SheetClose } from "@/components/ui/sheet"
import LogoutButton from "@/components/logout-button"

export default function MainNav() {
  const { data: session } = useSession()

  // Do not render anything when the user is not authenticated.
  if (!session) return null

  return (
    <>
      {/* Desktop navigation */}
      <div className="ml-auto hidden md:flex items-center gap-2">
        <nav className="flex gap-2">
          <Button variant="ghost" asChild>
            <Link href="/drp">DRP System</Link>
          </Button>
          <Button variant="ghost" asChild>
            <Link href="/dic">DIC System</Link>
          </Button>
          <Button variant="ghost" asChild>
            <Link href="/antibiogram">Antibiogram System</Link>
          </Button>
          <Button variant="ghost" asChild>
            <Link href="/antibiogram/report">Antibiogram Reports</Link>
          </Button>
        </nav>
        <LogoutButton />
      </div>

      {/* Mobile navigation */}
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="ml-auto md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Open menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" aria-label="Main navigation" className="p-0 md:hidden w-64">
          <nav className="flex flex-col p-4 gap-2">
            <SheetClose asChild>
              <Button variant="ghost" asChild className="w-full justify-start">
                <Link href="/drp">DRP System</Link>
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button variant="ghost" asChild className="w-full justify-start">
                <Link href="/dic">DIC System</Link>
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button variant="ghost" asChild className="w-full justify-start">
                <Link href="/antibiogram">Antibiogram System</Link>
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button variant="ghost" asChild className="w-full justify-start">
                <Link href="/antibiogram/report">Antibiogram Reports</Link>
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <LogoutButton />
            </SheetClose>
          </nav>
        </SheetContent>
      </Sheet>
    </>
  )
} 