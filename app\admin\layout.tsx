import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import Link from "next/link"
import { authOptions } from "@/lib/auth"
import "@/app/globals.css"
import { ReactNode } from "react"
import { AdminNav } from "@/components/admin-nav"

export default async function AdminLayout({ children }: { children: ReactNode }) {
  const session = await getServerSession(authOptions)
  if (!session || session.user.role !== "admin") {
    redirect("/")
  }

  return (
    <div className="flex min-h-screen">
      <aside className="w-64 border-r p-6 bg-background">
        <AdminNav />
      </aside>
      <main className="flex-1 p-4 overflow-y-auto">{children}</main>
    </div>
  )
} 