"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON>alogTit<PERSON> } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { toast } from "@/hooks/use-toast"
import { Separator } from "@/components/ui/separator"

interface Antibiogram {
  id: string
  patient_id: string
  patient_name: string | null
  unit: string
  date: string
  resistance_type: string | null
  culture_type: string
  microorganism_id: string | null
  microorganism_name: string | null
  microorganism_type: string | null
}

interface AntibioticResultRow {
  antibiotic_name: string
  antibiotic_class: string | null
  result: string
}

interface Props {
  entry: Antibiogram
  onClose: () => void
  onSuccess: () => void
}

const microorganisms = [
  { id: "GP001", name: "<PERSON><PERSON> (MSSA)", type: "Gram-positive" },
  { id: "GP002", name: "MRSA", type: "Gram-positive" },
  { id: "GP003", name: "Streptococcus", type: "Gram-positive" },
  { id: "GP004", name: "Enterococcus", type: "Gram-positive" },
  { id: "GP005", name: "CONS", type: "Gram-positive" },
  { id: "GN001", name: "Klebsiella", type: "Gram-negative" },
  { id: "GN002", name: "E-Coli", type: "Gram-negative" },
  { id: "GN003", name: "Pseudomonas", type: "Gram-negative" },
  { id: "GN004", name: "Proteus", type: "Gram-negative" },
  { id: "GN005", name: "Acinetobacter", type: "Gram-negative" },
  { id: "GN006", name: "Enterobacter", type: "Gram-negative" },
]

const antibiotics = [
  { name: "Amikin", class: "Aminoglycosides" },
  { name: "Augmentin", class: "Penicillins" },
  { name: "Azetronem", class: "Monobactams" },
  { name: "Cipro", class: "Fluoroquinolones" },
  { name: "Claforan", class: "Cephalosporins" },
  { name: "Colistin", class: "Polymyxins" },
  { name: "Dalacin", class: "Lincosamides" },
  { name: "Doxycycline", class: "Tetracyclines" },
  { name: "Fosfomycin", class: "Phosphonic acids" },
  { name: "Fortum", class: "Cephalosporins" },
  { name: "Gentamycin", class: "Aminoglycosides" },
  { name: "Inzanz", class: "Carbapenems" },
  { name: "Maxipeme", class: "Cephalosporins" },
  { name: "Meropenem", class: "Carbapenems" },
  { name: "Moxiflox", class: "Fluoroquinolones" },
  { name: "Rocephen", class: "Cephalosporins" },
  { name: "Septrin", class: "Folate inhibitors" },
  { name: "Sulperazone", class: "Cephalosporins" },
  { name: "Targocid", class: "Glycopeptides" },
  { name: "Tavanic", class: "Fluoroquinolones" },
  { name: "Tazocin", class: "Penicillins" },
  { name: "Tienam", class: "Carbapenems" },
  { name: "Tygacil", class: "Glycylcyclines" },
  { name: "Unasyn", class: "Penicillins" },
  { name: "Uvamin", class: "Nitrofurans" },
  { name: "Vancomycin", class: "Glycopeptides" },
  { name: "Zavicefta", class: "Cephalosporins" },
  { name: "Zithromax", class: "Macrolides" },
  { name: "Zyvox", class: "Oxazolidinones" },
].sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()))

const unitOptions = ["ICU", "IMC", "InPatient"]
const cultureTypes = ["Blood", "Sputum", "Urine", "Wound"]
const resistanceTypes = [
  { value: "MDR", label: "MDR" },
  { value: "XDR", label: "XDR" },
  { value: "Pan-DR", label: "Pan-DR" },
]

export function EditAntibiogramModal({ entry, onClose, onSuccess }: Props) {
  const [formData, setFormData] = useState({
    date: entry.date.split("T")[0],
    patientId: entry.patient_id,
    patientName: entry.patient_name || "",
    unit: entry.unit,
    resistanceType: entry.resistance_type || "",
    cultureType: entry.culture_type,
    microorganism: entry.microorganism_id || "",
    antibiotics: {} as Record<string, string>,
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    const fetchResults = async () => {
      const { data, error } = await supabase
        .from("antibiotic_results")
        .select("antibiotic_name, result")
        .eq("antibiogram_id", entry.id)

      if (!error && data) {
        const resultsRec: Record<string, string> = {}
        data.forEach((r: any) => {
          resultsRec[r.antibiotic_name] = r.result
        })
        setFormData((prev) => ({ ...prev, antibiotics: resultsRec }))
      }
      setLoading(false)
    }
    fetchResults()
  }, [entry.id])

  const handleAntibioticChange = (ab: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      antibiotics: { ...prev.antibiotics, [ab]: value },
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Update antibiogram row
      const selectedOrganism = microorganisms.find((o) => o.id === formData.microorganism)
      const { error: upErr } = await supabase
        .from("antibiograms")
        .update({
          patient_id: formData.patientId,
          patient_name: formData.patientName,
          unit: formData.unit,
          date: formData.date,
          resistance_type: formData.resistanceType || null,
          culture_type: formData.cultureType,
          microorganism_id: selectedOrganism?.id || null,
          microorganism_name: selectedOrganism?.name || null,
          microorganism_type: selectedOrganism?.type || null,
        })
        .eq("id", entry.id)

      if (upErr) throw upErr

      // Delete old antibiotic_results
      const { error: delErr } = await supabase.from("antibiotic_results").delete().eq("antibiogram_id", entry.id)
      if (delErr) throw delErr

      // Prepare new results
      const newRows = antibiotics.map((ab) => ({
        antibiogram_id: entry.id,
        antibiotic_name: ab.name,
        antibiotic_class: ab.class,
        result: formData.antibiotics[ab.name] || "None",
      }))
      const { error: insErr } = await supabase.from("antibiotic_results").insert(newRows)
      if (insErr) throw insErr

      toast({ title: "Updated", description: "Antibiogram updated successfully." })
      onSuccess()
      onClose()
    } catch (e: any) {
      toast({ title: "Error", description: e.message || "Failed to update", variant: "destructive" })
    } finally {
      setSaving(false)
    }
  }

  if (loading) return null

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-3xl overflow-y-auto max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Edit Antibiogram</DialogTitle>
          <DialogDescription>Modify data then press save.</DialogDescription>
        </DialogHeader>

        {/* Form */}
        <div className="space-y-6">
          {/* Top info */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <Label>Date</Label>
              <Input type="date" value={formData.date} onChange={(e) => setFormData({ ...formData, date: e.target.value })} />
            </div>
            <div className="space-y-1">
              <Label>Patient ID</Label>
              <Input value={formData.patientId} onChange={(e) => setFormData({ ...formData, patientId: e.target.value })} />
            </div>
            <div className="space-y-1">
              <Label>Patient Name</Label>
              <Input value={formData.patientName} onChange={(e) => setFormData({ ...formData, patientName: e.target.value })} />
            </div>
            <div className="space-y-1">
              <Label>Unit</Label>
              <Select value={formData.unit} onValueChange={(v) => setFormData({ ...formData, unit: v })}>
                <SelectTrigger>
                  <SelectValue placeholder="Unit" />
                </SelectTrigger>
                <SelectContent>
                  {unitOptions.map((u) => (
                    <SelectItem key={u} value={u}>
                      {u}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <Label>Resistance Type</Label>
              <Select value={formData.resistanceType} onValueChange={(v) => setFormData({ ...formData, resistanceType: v })}>
                <SelectTrigger>
                  <SelectValue placeholder="Resistance" />
                </SelectTrigger>
                <SelectContent>
                  {resistanceTypes.map((r) => (
                    <SelectItem key={r.value} value={r.value}>
                      {r.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label>Culture Type</Label>
              <Select value={formData.cultureType} onValueChange={(v) => setFormData({ ...formData, cultureType: v })}>
                <SelectTrigger>
                  <SelectValue placeholder="Culture" />
                </SelectTrigger>
                <SelectContent>
                  {cultureTypes.map((c) => (
                    <SelectItem key={c} value={c}>
                      {c}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-1">
            <Label>Microorganism</Label>
            <Select value={formData.microorganism} onValueChange={(v) => setFormData({ ...formData, microorganism: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Organism" />
              </SelectTrigger>
              <SelectContent>
                <div className="px-2 py-1 text-xs font-semibold text-green-600 bg-green-50">Gram-positive</div>
                {microorganisms
                  .filter((o) => o.type === "Gram-positive")
                  .map((o) => (
                    <SelectItem key={o.id} value={o.id}>
                      {o.name}
                    </SelectItem>
                  ))}
                <div className="px-2 py-1 text-xs font-semibold text-red-600 bg-red-50">Gram-negative</div>
                {microorganisms
                  .filter((o) => o.type === "Gram-negative")
                  .map((o) => (
                    <SelectItem key={o.id} value={o.id}>
                      {o.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Antibiotic results */}
          <div className="space-y-2 max-h-[50vh] overflow-y-auto pr-2">
            {antibiotics.map((ab) => (
              <div key={ab.name} className="flex items-center justify-between border rounded p-2 text-xs">
                <span>
                  <strong>{ab.name}</strong> <span className="text-muted-foreground">({ab.class})</span>
                </span>
                <div className="flex gap-4">
                  {[
                    { label: "Sensitive", value: "Sensitive", color: "text-green-600" },
                    { label: "Resistant", value: "Resistant", color: "text-red-600" },
                    { label: "None", value: "None", color: "text-gray-600" },
                  ].map((opt) => (
                    <label key={opt.value} className={`flex items-center gap-1 cursor-pointer ${opt.color}`}>
                      <input
                        type="radio"
                        name={`ab-${ab.name}`}
                        value={opt.value}
                        checked={formData.antibiotics[ab.name] === opt.value}
                        onChange={(e) => handleAntibioticChange(ab.name, e.target.value)}
                        className="scale-75"
                      />
                      {opt.label}
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? "Saving..." : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 