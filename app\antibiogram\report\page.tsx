"use client"

import { useEffect, useState, useMemo } from "react"
import { supabase } from "@/lib/supabase"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Loader2 } from "lucide-react"
import Link from "next/link"
// Dynamically import ExcelJS only when needed (reduces bundle size)
// No static import here to avoid including heavy ExcelJS in the initial bundle
// import ExcelJS from "exceljs" (dynamic import used in export handler)

interface ResultRow {
  antibiotic_name: string
  result: string
  antibiograms: {
    id: string | number
    microorganism_name: string
    microorganism_type: string
    date: string
    resistance_type: string | null
  }
}

export default function AntibiogramReportPage() {
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<ResultRow[]>([])
  const [hospitalName, setHospitalName] = useState("SCU Hospital Antibiogram")
  const [exportLoading, setExportLoading] = useState(false)

  // Antibiotic categories grouped by drug class (per user requirements)
  const antibioticCategories: Record<string, string[]> = {
    Penicillins: ["Augmentin", "Unasyn", "Tazocin"],
    Cephalosporin: [
      "Claforan",
      "Rocephen",
      "Fortum",
      "Sulperazone",
      "Maxipeme",
      "Zavicefta"
    ],
    Carbapenems: ["Meropenem", "Tienam", "Inzanz"],
    "MB.": ["Azetronem"],
    Quinolones: ["Cipro", "Tavanic", "Moxiflox"],
    "AG.": ["Gentamycin", "Amikin"],
    "Anti-MRSA": ["Vancomycin", "Zyvox", "Targocid"],
    Miscellaneous: ["Doxycycline", "Zithromax", "Dalacin", "Tygacil", "Colistin"],
    "UTI-specific": ["Uvamin", "Fosfomycin", "Septrin"]
  }

  // Flatten antibiotics for data processing while preserving order
  const allAntibiotics: string[] = useMemo(() => Object.values(antibioticCategories).flat(), [])

  // Helper to normalise names for consistent keying (case / special-chars insensitive)
  const sanitise = (str: string) => str.toLowerCase().replace(/[^a-z0-9]/g, "")

  useEffect(() => {
    // Modified to use January 1, 2024 as start date
    const now = new Date()
    const startDateObj = new Date(2024, 0, 1) // January 1, 2024
    const todayStr = now.toISOString().split("T")[0]
    setStartDate(startDateObj.toISOString().split("T")[0])
    setEndDate(todayStr)
  }, [])

  const fetchData = async () => {
    // Guard: ensure both dates are provided before querying
    if (!startDate || !endDate) return

    setLoading(true)

    try {
      console.log(`Fetching antibiogram data from ${startDate} to ${endDate}`)

      // Fetch antibiograms with their antibiotic results using JOIN
      const { data: resultData, error } = await supabase
        .from("antibiograms")
        .select(
          `id, microorganism_name, microorganism_type, date, resistance_type,
           antibiotic_results(antibiotic_name, result)`
        )
        .gte('date', startDate)
        .lte('date', endDate)
        .order('date', { ascending: false })

      if (error) throw error

      const transformedRows: ResultRow[] = []
      resultData?.forEach((antibiogram: any) => {
        if (antibiogram.antibiotic_results && antibiogram.antibiotic_results.length > 0) {
          antibiogram.antibiotic_results.forEach((result: any) => {
            transformedRows.push({
              antibiotic_name: result.antibiotic_name,
              result: result.result,
              antibiograms: {
                id: antibiogram.id,
                microorganism_name: antibiogram.microorganism_name,
                microorganism_type: antibiogram.microorganism_type,
                date: antibiogram.date,
                resistance_type: antibiogram.resistance_type,
              },
            })
          })
        }
      })

      console.log(`Fetched ${resultData?.length || 0} antibiograms, produced ${transformedRows.length} result rows`)
      setData(transformedRows)
    } catch (error: any) {
      console.error('Error fetching antibiogram data:', error)
    } finally {
      setLoading(false)
    }
  }

  // Aggregate to { [organism]: { [antibiotic]: { sensitive, resistant } } }
  const aggregated = useMemo(() => {
    const acc: Record<string, Record<string, { s: number; r: number }>> = {}
    data.forEach((row) => {
      const organismKey = sanitise(row.antibiograms.microorganism_name || "Unknown")
      const abKey = sanitise(row.antibiotic_name)
      if (!acc[organismKey]) acc[organismKey] = {}
      if (!acc[organismKey][abKey]) acc[organismKey][abKey] = { s: 0, r: 0 }
      if (row.result === "Sensitive") acc[organismKey][abKey].s += 1
      if (row.result === "Resistant") acc[organismKey][abKey].r += 1
    })
    return acc
  }, [data])

  // Distinct isolate counting -----------------------------------------
  const { isolatesByOrganism, totalIsolates } = useMemo(() => {
    const organismIsoSet: Record<string, Set<string>> = {}
    const allIsoSet: Set<string> = new Set()

    data.forEach((row) => {
      const orgKey = sanitise(row.antibiograms.microorganism_name || "Unknown")
      // Prefer db id; fallback composite key to ensure uniqueness per isolate
      const isoKey =
        row.antibiograms.id !== undefined && row.antibiograms.id !== null
          ? String(row.antibiograms.id)
          : `${orgKey}-${row.antibiograms.date}`

      if (!organismIsoSet[orgKey]) organismIsoSet[orgKey] = new Set<string>()
      organismIsoSet[orgKey].add(isoKey)
      allIsoSet.add(isoKey)
    })

    const counts: Record<string, number> = {}
    Object.entries(organismIsoSet).forEach(([k, set]) => {
      counts[k] = set.size
    })

    return { isolatesByOrganism: counts, totalIsolates: allIsoSet.size }
  }, [data])

  // Predefined organism rows to ensure table is always fully visible
  const gramNegativeOrganisms = [
    "Klebsiella",
    "E-Coli",
    "Pseudomonas",
    "Proteus",
    "Acinetobacter",
    "Enterobacter"
  ]

  const gramPositiveOrganisms = [
    "Staph (MSSA)",
    "MRSA",
    "Streptococcus",
    "Enterococcus",
    "CONS"
  ]

  const calcPercentage = (organism: string, antibiotic: string) => {
    const rec = aggregated[sanitise(organism)]?.[sanitise(antibiotic)]
    if (!rec) return null
    const total = rec.s + rec.r
    if (total === 0) return null
    return Math.round((rec.s / total) * 100)
  }

  const getColorClass = (pct: number | null) => {
    if (pct === null) return ""
    if (pct >= 70) return "bg-green-200"
    if (pct >= 40 && pct < 70) return "bg-yellow-200"
    return "bg-red-200"
  }

  // Calculate summary statistics
  const calculateSummary = () => {
    // Use sets so that each isolate (antibiogram) is only counted once
    const gramNegSet: Set<string> = new Set()
    const gramPosSet: Set<string> = new Set()
    const mrsaSet: Set<string> = new Set()

    const mdrSet: Set<string> = new Set()
    const xdrSet: Set<string> = new Set()
    const pdrSet: Set<string> = new Set()

    const allIsoSet: Set<string> = new Set()

    data.forEach((row) => {
      // Build a unique key for the isolate (antibiogram)
      const isoId =
        row.antibiograms.id !== undefined && row.antibiograms.id !== null
          ? String(row.antibiograms.id)
          : `${row.antibiograms.microorganism_name}-${row.antibiograms.date}`

      allIsoSet.add(isoId)

      // Organism type classification
      if (row.antibiograms.microorganism_type === "Gram-negative") {
        gramNegSet.add(isoId)
      } else if (row.antibiograms.microorganism_type === "Gram-positive") {
        gramPosSet.add(isoId)
      }

      // MRSA isolates
      if (row.antibiograms.microorganism_name === "MRSA") {
        mrsaSet.add(isoId)
      }

      // Resistance pattern classification
      const resType = row.antibiograms.resistance_type
      if (resType === "MDR") mdrSet.add(isoId)
      else if (resType === "XDR") xdrSet.add(isoId)
      else if (resType === "Pan-DR" || resType === "PDR") pdrSet.add(isoId)
    })

    const totalIsolates = allIsoSet.size || 1 // avoid divide-by-zero

    // Counts
    const gramNegCount = gramNegSet.size
    const gramPosCount = gramPosSet.size
    const mrsaCount = mrsaSet.size

    // Independent rounding for Gram-negative and Gram-positive
    const gramNegPercent = Math.round((gramNegCount / totalIsolates) * 100)
    const gramPosPercent = Math.round((gramPosCount / totalIsolates) * 100)

    // Resistance pattern percentages – apply threshold logic
    const threshold = 30
    const belowThreshold = totalIsolates <= threshold

    const mrsaPercent = belowThreshold ? 0 : Math.round((mrsaCount / totalIsolates) * 100)

    const mdrCount = mdrSet.size
    const xdrCount = xdrSet.size
    const pdrCount = pdrSet.size

    const mdrPct = belowThreshold ? 0 : Math.round((mdrCount / totalIsolates) * 100)
    const xdrPct = belowThreshold ? 0 : Math.round((xdrCount / totalIsolates) * 100)
    const pdrPct = belowThreshold ? 0 : Math.round((pdrCount / totalIsolates) * 100)

    return {
      gramNegCount,
      gramNegPercent,
      gramPosCount,
      gramPosPercent,
      mrsaCount,
      mrsaPercent,
      mdrCount,
      mdrPct,
      xdrCount,
      xdrPct,
      pdrCount,
      pdrPct,
      belowThreshold,
    }
  }

  const summary = useMemo(calculateSummary, [data])

  // ---------------- Export helpers -----------------
  const buildExportRows = () => {
    const header = [
      "Organism",
      "Isolates",
      "Percent",
      ...allAntibiotics,
    ]

    const organisms = [...gramNegativeOrganisms, ...gramPositiveOrganisms]

    const rows = organisms.map((organism) => {
      const isolateCount = isolatesByOrganism[sanitise(organism)] || 0
      const percent = totalIsolates
        ? Math.round((isolateCount / totalIsolates) * 100)
        : 0

      const abPcts = allAntibiotics.map((ab) => {
        const pct = calcPercentage(organism, ab)
        return pct !== null ? pct : ""
      })

      return [organism, isolateCount, percent, ...abPcts]
    })

    const meta: (string | number)[][] = [
      ["Hospital", hospitalName],
      ["Date Range", `${startDate} to ${endDate}`],
      [
        "Exported At",
        new Date().toLocaleString(undefined, {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        }),
      ],
      [], // blank row
    ]

    return [...meta, header, ...rows]
  }

  const downloadBlob = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    a.remove()
    URL.revokeObjectURL(url)
  }

  const handleExport = async (format: "csv" | "xlsx") => {
    setExportLoading(true)
    try {
      const rows = buildExportRows()

      const timestamp = new Date()
        .toISOString()
        .replace(/[:.]/g, "-")
        .split("T")
        .join("_")

      const filenameBase = `antibiogram_report_${timestamp}`

      if (format === "csv") {
        const escapeCell = (cell: any) => {
          if (cell == null) return ""
          const str = String(cell)
          if (/[,"\n]/.test(str)) {
            return `"${str.replace(/"/g, '""')}"`
          }
          return str
        }
        const csvContent = rows.map((r) => r.map(escapeCell).join(",")).join("\n")
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8",
        })
        downloadBlob(blob, `${filenameBase}.csv`)
      } else {
        // Styled XLSX using ExcelJS
        // Dynamically load the library only when an XLSX export is requested
        // @ts-ignore – exceljs provides its own types but dynamic import may confuse TS, so cast to any
        const ExcelJS: any = (await import("exceljs/dist/exceljs.min.js")).default

        const workbook = new ExcelJS.Workbook()
        const worksheet = workbook.addWorksheet("Report")

        // ---- Meta information rows ----
        worksheet.addRow(["Hospital", hospitalName])
        worksheet.addRow(["Date Range", `${startDate} to ${endDate}`])
        worksheet.addRow([
          "Exported At",
          new Date().toLocaleString(undefined, {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
          }),
        ])
        worksheet.addRow([]) // blank spacer row

        const totalColumns = 3 + allAntibiotics.length // 3 leading cols + antibiotic cols

        // Reusable thin border style
        const border = {
          top: { style: "thin" as const, color: { argb: "FF000000" } },
          left: { style: "thin" as const, color: { argb: "FF000000" } },
          bottom: { style: "thin" as const, color: { argb: "FF000000" } },
          right: { style: "thin" as const, color: { argb: "FF000000" } },
        }

        const allColCount = totalColumns

        // ---- Drug-class header row (merged) ----
        const classHeaderRow = worksheet.addRow(new Array(totalColumns).fill(""))
        let colPointer = 4 // first 3 columns are blank / labels
        Object.entries(antibioticCategories).forEach(([className, list]) => {
          worksheet.mergeCells(classHeaderRow.number, colPointer, classHeaderRow.number, colPointer + list.length - 1)
          const cell = classHeaderRow.getCell(colPointer)
          cell.value = className
          cell.alignment = { horizontal: "center", vertical: "middle" }
          cell.font = { bold: true, size: 12 }
          // Apply border across merged area
          for (let c = colPointer; c < colPointer + list.length; c++) {
            classHeaderRow.getCell(c).border = border
          }
          colPointer += list.length
        })

        // Add borders to first three header cells (blank/isolate/percent placeholders)
        for (let c = 1; c <= 3; c++) {
          classHeaderRow.getCell(c).border = border
        }

        // ---- Antibiotic names header row ----
        const abHeaderRow = worksheet.addRow(["", "Isolates", "Percent", ...allAntibiotics])
        abHeaderRow.eachCell((cell: any, colNumber: number) => {
          cell.font = { bold: true, size: 12 }
          cell.alignment = { horizontal: "center" }
          cell.border = border
          if (colNumber > 3) {
            cell.fill = { type: "pattern", pattern: "solid", fgColor: { argb: "FFF2F2F2" } }
          }
        })

        // Helper to add organism rows with colouring
        const addOrganismRow = (organism: string, headerBg: string) => {
          const isolateCount = isolatesByOrganism[sanitise(organism)] || 0
          const percent = totalIsolates ? Math.round((isolateCount / totalIsolates) * 100) : 0

          const row = worksheet.addRow([
            organism,
            isolateCount,
            percent,
            ...allAntibiotics.map((ab) => {
              const pct = calcPercentage(organism, ab)
              return pct !== null ? pct : ""
            }),
          ])

          // Style first cell background to match UI section colour
          row.getCell(1).fill = { type: "pattern", pattern: "solid", fgColor: { argb: headerBg } }
          row.getCell(1).border = border
          row.getCell(2).border = border
          row.getCell(3).border = border

          // Alignment for numeric columns
          row.getCell(2).alignment = { horizontal: "center" }
          row.getCell(3).alignment = { horizontal: "center" }

          // Colour antibiotic percentage cells based on thresholds
          allAntibiotics.forEach((_ab, idx) => {
            const cell = row.getCell(4 + idx)
            cell.alignment = { horizontal: "center" }
            cell.border = border
            if (typeof cell.value === "number") {
              const pct = cell.value as number
              let colour = "FFFFCCCC" // red (<40)
              if (pct >= 70) colour = "FFCCFFCC" // green
              else if (pct >= 40) colour = "FFFFFFCC" // yellow
              cell.fill = { type: "pattern", pattern: "solid", fgColor: { argb: colour } }
            }
          })
        }

        // ---- Gram-negative block ----
        const gnHeader = worksheet.addRow(["Gram Negative"]) // value only to first cell
        worksheet.mergeCells(gnHeader.number, 1, gnHeader.number, allColCount)
        gnHeader.font = { bold: true }
        gnHeader.alignment = { horizontal: "center" }
        gnHeader.fill = { type: "pattern", pattern: "solid", fgColor: { argb: "FFEBF5FF" } }
        // Apply border across merged range
        for (let c = 1; c <= allColCount; c++) {
          gnHeader.getCell(c).border = border
        }

        gramNegativeOrganisms.forEach((org) => addOrganismRow(org, "FFEBF5FF"))

        // Spacer row
        worksheet.addRow([])

        // ---- Gram-positive block ----
        const gpHeader = worksheet.addRow(["Gram Positive"])
        worksheet.mergeCells(gpHeader.number, 1, gpHeader.number, allColCount)
        gpHeader.font = { bold: true }
        gpHeader.alignment = { horizontal: "center" }
        gpHeader.fill = { type: "pattern", pattern: "solid", fgColor: { argb: "FFFFEEF0" } }
        for (let c = 1; c <= allColCount; c++) {
          gpHeader.getCell(c).border = border
        }

        gramPositiveOrganisms.forEach((org) => addOrganismRow(org, "FFFFEEF0"))

        // ---- Summary row ----
        const summaryRow = worksheet.addRow(["Total", totalIsolates])
        worksheet.mergeCells(summaryRow.number, 3, summaryRow.number, allColCount) // merge remaining cells
        summaryRow.font = { bold: true }
        for (let c = 1; c <= 2; c++) {
          summaryRow.getCell(c).border = border
        }
        for (let c = 3; c <= allColCount; c++) {
          summaryRow.getCell(c).border = border
        }

        // ---------------- Summary Statistics Section ----------------
        worksheet.addRow([]) // spacer

        // Helper to add a small table (3 columns) starting at current last row.
        const addSmallTable = (
          title: string,
          rows: Array<[string, number, string | number]>,
        ) => {
          // Title row (merged over 3 columns): bold
          const titleRow = worksheet.addRow([title])
          worksheet.mergeCells(titleRow.number, 1, titleRow.number, 3)
          titleRow.font = { bold: true, size: 12 }
          titleRow.alignment = { horizontal: "center" }

          // Header row
          const header = worksheet.addRow(["Category", "Count", "%"])
          header.font = { bold: true, size: 12 }
          header.alignment = { horizontal: "center" }

          // Data rows
          rows.forEach((r: [string, number, string | number]) => {
            const dataRow = worksheet.addRow(r)
            dataRow.alignment = { horizontal: "center" }
          })

          // Apply borders to the 3-column block (title row excluded)
          const startRow = header.number
          const endRow = worksheet.lastRow.number
          for (let i = startRow; i <= endRow; i++) {
            for (let c = 1; c <= 3; c++) {
              worksheet.getRow(i).getCell(c).border = border
            }
          }

          worksheet.addRow([]) // spacer after each table
        }

        // Table 1: Gram classification
        addSmallTable("Gram Classification", [
          ["Gram Negative", summary.gramNegCount, `${summary.gramNegPercent}%`],
          ["Gram Positive", summary.gramPosCount, `${summary.gramPosPercent}%`],
        ])

        // Table 2: MRSA
        addSmallTable("MRSA Status", [
          ["MRSA", summary.mrsaCount, summary.belowThreshold ? "Insufficient" : `${summary.mrsaPercent}%`],
        ])

        // Table 3: Drug Resistance Classification
        addSmallTable("Drug Resistance Classification", [
          ["MDR", summary.mdrCount, summary.belowThreshold ? "Insufficient" : `${summary.mdrPct}%`],
          ["XDR", summary.xdrCount, summary.belowThreshold ? "Insufficient" : `${summary.xdrPct}%`],
          ["PDR", summary.pdrCount, summary.belowThreshold ? "Insufficient" : `${summary.pdrPct}%`],
        ])

        // ---------------- Global styling pass ----------------
        worksheet.eachRow({ includeEmpty: false }, (row: any) => {
          row.eachCell((cell: any) => {
            // Ensure default font size and border if not set.
            cell.font = { ...cell.font, size: 12 }
            cell.border = cell.border ?? border
          })
        })

        // ---- Column widths ----
        worksheet.columns.forEach((col: any) => {
          col.width = 12
        })

        // Generate output and trigger download
        const buffer = await workbook.xlsx.writeBuffer()
        const blob = new Blob([buffer], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        })
        downloadBlob(blob, `${filenameBase}.xlsx`)
      }
    } catch (err) {
      console.error("Export failed", err)
    } finally {
      setExportLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-6 relative print:hidden">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Antibiogram Report</h1>
        </div>
        <div className="absolute top-0 right-0">
          <Link href="/antibiogram" className="text-blue-600 hover:underline text-sm">
            ← Back to Management
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader className="print:hidden">
      
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 flex-wrap items-end print:hidden">
            <div className="space-y-1">
              <label className="block text-sm font-medium">Hospital Name</label>
              <Input 
                type="text" 
                value={hospitalName} 
                onChange={(e) => setHospitalName(e.target.value)} 
                placeholder="Enter hospital name"
              />
            </div>
            <div className="space-y-1">
              <label className="block text-sm font-medium">Start Date</label>
              <Input type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
            </div>
            <div className="space-y-1">
              <label className="block text-sm font-medium">End Date</label>
              <Input type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
            </div>
            <Button onClick={fetchData} disabled={loading} className="mt-5">
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Apply"}
            </Button>
            <Button
              variant="outline"
              className="mt-5 print:hidden"
              onClick={() => window.print()}
            >
              Print
            </Button>

            {/* Export dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="mt-5 print:hidden"
                  disabled={exportLoading}
                >
                  {exportLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    "Export"
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onSelect={() => handleExport("csv")}>CSV</DropdownMenuItem>
                <DropdownMenuItem onSelect={() => handleExport("xlsx")}>XLSX</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Report Table */}
          {loading ? (
            <div className="flex justify-center py-10">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : (
            <>
              {/* Antibiogram Table */}
              <div className="overflow-x-auto mt-6">
                <div className="text-center font-bold text-lg mb-2 border-b-2 border-black">
                  {hospitalName} (From {startDate} to {endDate})
                </div>
                <div className="text-center font-bold mb-4 print:hidden">
                  Cumulative Antimicrobial Susceptibility Report* (Percent Susceptible)
                </div>
                
                {/* Color code legend */}
                <div className="mb-4 text-xs print:hidden">
                  <span className="font-bold">Colour Code:</span>
                  <span className="ml-2 bg-green-200 px-2 py-1">Green 70-100%,</span>
                  <span className="ml-2 bg-yellow-200 px-2 py-1">Yellow 40-69%,</span>
                  <span className="ml-2 bg-red-200 px-2 py-1">Orange 40-59%</span>
                </div>
                
                <table className="w-full border-collapse border border-gray-300 text-xs">
                  <thead>
                    {/* Dynamic drug-class headers */}
                    <tr className="border-b border-gray-300">
                      <th className="border border-gray-300 p-1"></th>
                      <th className="border border-gray-300 p-1"></th>
                      <th className="border border-gray-300 p-1"></th>
                      {Object.entries(antibioticCategories).map(([className, list]) => (
                        <th
                          key={className}
                          colSpan={list.length}
                          className="border border-gray-300 p-1 text-center"
                        >
                          {className}
                        </th>
                      ))}
                    </tr>
                    {/* Antibiotic names row */}
                    <tr className="border-b border-gray-300">
                      <th className="border border-gray-300 p-1 bg-gray-100"></th>
                      <th className="border border-gray-300 p-1 bg-gray-200">Isolates</th>
                      <th className="border border-gray-300 p-1 bg-gray-200">Percent</th>
                      {allAntibiotics.map((antibiotic) => (
                        <th
                          key={antibiotic}
                          className="border border-gray-300 bg-gray-100 text-center"
                          style={{
                            width: "30px",
                            height: "120px", 
                            position: "relative",
                            padding: "0"
                          }}
                        >
                          <div 
                            style={{
                              position: "absolute",
                              bottom: "8px",
                              left: "50%",
                              transform: "translateX(-50%) rotate(-90deg)",
                              transformOrigin: "center",
                              whiteSpace: "nowrap",
                              width: "20px"
                            }}
                          >
                            {antibiotic}
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {/* Gram Negative Section */}
                    <tr className="border-b border-gray-300 bg-blue-50">
                      <td
                        colSpan={allAntibiotics.length + 3}
                        className="border border-gray-300 p-1 font-bold"
                      >
                        Gram Negative
                      </td>
                    </tr>
                    
                    {gramNegativeOrganisms.map((organism, index) => (
                      <tr key={organism} className="border-b border-gray-300">
                        <td className="border border-gray-300 p-1 bg-blue-50">{organism}</td>
                        <td className="border border-gray-300 p-1 bg-gray-100 text-center">
                          {isolatesByOrganism[sanitise(organism)] || 0}
                        </td>
                        <td className="border border-gray-300 p-1 bg-gray-100 text-center">
                          {totalIsolates
                            ? Math.round(
                                ((isolatesByOrganism[sanitise(organism)] || 0) /
                                  totalIsolates) *
                                  100
                              )
                            : 0}
                          %
                        </td>
                        
                        {/* Generate cells for each antibiotic */}
                        {allAntibiotics.map((antibiotic) => {
                          const pct = calcPercentage(organism, antibiotic)
                          return (
                            <td 
                              key={`${organism}-${antibiotic}`} 
                              className={`border border-gray-300 text-center ${getColorClass(pct)}`}
                              style={{ width: "30px", padding: "4px" }}
                            >
                              {pct !== null ? `${pct}` : ""}
                            </td>
                          )
                        })}
                      </tr>
                    ))}
                    
                    {/* Empty row as separator */}
                    <tr className="border-b border-gray-300">
                      <td colSpan={allAntibiotics.length + 3} className="border border-gray-300 p-1"></td>
                    </tr>
                    
                    {/* Gram Positive Section */}
                    <tr className="border-b border-gray-300 bg-rose-50">
                      <td
                        colSpan={allAntibiotics.length + 3}
                        className="border border-gray-300 p-1 font-bold"
                      >
                        Gram Positive
                      </td>
                    </tr>
                    
                    {gramPositiveOrganisms.map((organism) => (
                      <tr key={organism} className="border-b border-gray-300">
                        <td className="border border-gray-300 p-1 bg-rose-50">{organism}</td>
                        <td className="border border-gray-300 p-1 bg-gray-100 text-center">
                          {isolatesByOrganism[sanitise(organism)] || 0}
                        </td>
                        <td className="border border-gray-300 p-1 bg-gray-100 text-center">
                          {totalIsolates
                            ? Math.round(
                                ((isolatesByOrganism[sanitise(organism)] || 0) /
                                  totalIsolates) *
                                  100
                              )
                            : 0}
                          %
                        </td>
                        
                        {/* Generate cells for each antibiotic */}
                        {allAntibiotics.map((antibiotic) => {
                          const pct = calcPercentage(organism, antibiotic)
                          return (
                            <td 
                              key={`${organism}-${antibiotic}`} 
                              className={`border border-gray-300 text-center ${getColorClass(pct)}`}
                              style={{ width: "30px", padding: "4px" }}
                            >
                              {pct !== null ? `${pct}` : ""}
                            </td>
                          )
                        })}
                      </tr>
                    ))}
                    
                  
                    
                    {/* Summary row */}
                    <tr className="border-b border-gray-300">
                      <td className="border border-gray-300 p-1">Total</td>
                      <td className="border border-gray-300 p-1 bg-gray-100 text-center">{totalIsolates}</td>
                      <td className="border border-gray-300 p-1"></td>
                      <td colSpan={allAntibiotics.length} className="border border-gray-300 p-1"></td>
                    </tr>
                  </tbody>
                </table>
                
                {/* Summary tables at the bottom */}
                <div className="flex mt-4 gap-4">
                  {/* Left summary table */}
                  <table className="border-collapse border border-gray-300 text-xs w-1/3">
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 p-1 font-bold">Category</td>
                        <td className="border border-gray-300 p-1 font-bold text-center">Count</td>
                        <td className="border border-gray-300 p-1 font-bold text-center">%</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 p-1 bg-blue-50">Gram Negative</td>
                        <td className="border border-gray-300 p-1 text-center">{summary.gramNegCount}</td>
                        <td className="border border-gray-300 p-1 text-center">{summary.gramNegPercent}%</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 p-1 bg-rose-50">Gram Positive</td>
                        <td className="border border-gray-300 p-1 text-center">{summary.gramPosCount}</td>
                        <td className="border border-gray-300 p-1 text-center">{summary.gramPosPercent}%</td>
                      </tr>
                    </tbody>
                  </table>
                  
                  {/* Middle MRSA table */}
                  <table className="border-collapse border border-gray-300 text-xs w-1/3">
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 p-1 font-bold">Category</td>
                        <td className="border border-gray-300 p-1 font-bold text-center">Count</td>
                        <td className="border border-gray-300 p-1 font-bold text-center">%</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 p-1 bg-rose-50">MRSA</td>
                        <td className="border border-gray-300 p-1 text-center">{summary.mrsaCount}</td>
                        <td className="border border-gray-300 p-1 text-center">
                          {summary.belowThreshold ? "Insufficient data" : `${summary.mrsaPercent}%`}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  
                  {/* Right MDR/XDR/PDR table */}
                  <table className="border-collapse border border-gray-300 text-xs w-1/3">
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 p-1 font-bold">Category</td>
                        <td className="border border-gray-300 p-1 font-bold text-center">Count</td>
                        <td className="border border-gray-300 p-1 font-bold text-center">%</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 p-1 bg-orange-200">MDR</td>
                        <td className="border border-gray-300 p-1 text-center">{summary.mdrCount}</td>
                        <td className="border border-gray-300 p-1 text-center">
                          {summary.belowThreshold ? "Insufficient data" : `${summary.mdrPct}%`}
                        </td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 p-1 bg-orange-200">XDR</td>
                        <td className="border border-gray-300 p-1 text-center">{summary.xdrCount}</td>
                        <td className="border border-gray-300 p-1 text-center">
                          {summary.belowThreshold ? "Insufficient data" : `${summary.xdrPct}%`}
                        </td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 p-1 bg-orange-200">PDR</td>
                        <td className="border border-gray-300 p-1 text-center">{summary.pdrCount}</td>
                        <td className="border border-gray-300 p-1 text-center">
                          {summary.belowThreshold ? "Insufficient data" : `${summary.pdrPct}%`}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}