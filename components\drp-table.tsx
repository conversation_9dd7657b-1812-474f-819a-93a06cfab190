"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Edit, Trash2, Search, Download, FileText, FileBarChart, Calendar, Eye } from "lucide-react"
import { supabase, type DrpEntry, type DrpType } from "@/lib/supabase"
import { useSession } from "next-auth/react"
import { toast } from "@/hooks/use-toast"
import { EditDrpModal } from "./edit-drp-modal"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  <PERSON><PERSON><PERSON>ialog<PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"

interface DrpTableProps {
  refreshTrigger: number
}

export function DrpTable({ refreshTrigger }: DrpTableProps) {
  const { data: session } = useSession()
  const [entries, setEntries] = useState<DrpEntry[]>([])
  const [filteredEntries, setFilteredEntries] = useState<DrpEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [editingEntry, setEditingEntry] = useState<DrpEntry | null>(null)
  const [viewingEntry, setViewingEntry] = useState<DrpEntry | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [filterResponse, setFilterResponse] = useState("all")
  const [filterPharmacist, setFilterPharmacist] = useState("all")
  // Fetch pharmacists dynamically from users table
  const [pharmacists, setPharmacists] = useState<string[]>([])
  // DRP types fetched from drp_types table
  const [drpTypes, setDrpTypes] = useState<DrpType[]>([])
  const [showReportDialog, setShowReportDialog] = useState(false)
  const [reportStartDate, setReportStartDate] = useState("")
  const [reportEndDate, setReportEndDate] = useState("")

  // Date range filter states
  const [dateFilterStartDate, setDateFilterStartDate] = useState("")
  const [dateFilterEndDate, setDateFilterEndDate] = useState("")
  const [showDateFilter, setShowDateFilter] = useState(false)

  useEffect(() => {
    fetchEntries()
  }, [refreshTrigger])

  // Fetch DRP types once on mount
  useEffect(() => {
    const fetchDrpTypes = async () => {
      const { data, error } = await supabase.from("drp_types").select("*").order("name")
      if (!error && data) {
        setDrpTypes(data)
      }
    }

    fetchDrpTypes()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [entries, searchTerm, filterType, filterResponse, filterPharmacist, dateFilterStartDate, dateFilterEndDate])

  // Set default date range to current month for reports
  useEffect(() => {
    const now = new Date()
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)

    setReportStartDate(firstDay.toISOString().split("T")[0])
    setReportEndDate(lastDay.toISOString().split("T")[0])
  }, [])

  // Fetch list of pharmacists from users table for filter dropdown
  useEffect(() => {
    const fetchPharmacists = async () => {
      const { data, error } = await supabase.from("users").select("name")
      if (!error && data) {
        setPharmacists(data.map((u: { name: string }) => u.name))
      }
    }
    fetchPharmacists()
  }, [])

  const fetchEntries = async () => {
    try {
      const { data, error } = await supabase.from("drp_entries").select("*").order("created_at", { ascending: false })

      if (error) throw error
      setEntries(data || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load DRP entries",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = entries

    // Date range filter
    if (dateFilterStartDate && dateFilterEndDate) {
      const startDate = new Date(dateFilterStartDate)
      const endDate = new Date(dateFilterEndDate)
      endDate.setHours(23, 59, 59, 999) // Include the entire end date

      filtered = filtered.filter((entry) => {
        const entryDate = new Date(entry.created_at)
        return entryDate >= startDate && entryDate <= endDate
      })
    } else if (dateFilterStartDate) {
      const startDate = new Date(dateFilterStartDate)
      filtered = filtered.filter((entry) => {
        const entryDate = new Date(entry.created_at)
        return entryDate >= startDate
      })
    } else if (dateFilterEndDate) {
      const endDate = new Date(dateFilterEndDate)
      endDate.setHours(23, 59, 59, 999)
      filtered = filtered.filter((entry) => {
        const entryDate = new Date(entry.created_at)
        return entryDate <= endDate
      })
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (entry) =>
          entry.patient_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
          entry.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          entry.medication.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    // DRP Type filter
    if (filterType !== "all") {
      filtered = filtered.filter((entry) => entry.drp_type === filterType)
    }

    // Doctor Response filter
    if (filterResponse !== "all") {
      filtered = filtered.filter((entry) => entry.doctor_response === filterResponse)
    }

    // Pharmacist filter
    if (filterPharmacist !== "all") {
      filtered = filtered.filter((entry) => entry.pharmacist_signature === filterPharmacist)
    }

    setFilteredEntries(filtered)
  }

  const handleDelete = async (id: number) => {
    try {
      const { error } = await supabase.from("drp_entries").delete().eq("id", id)

      if (error) throw error

      toast({
        title: "Success",
        description: "DRP entry deleted successfully",
      })

      fetchEntries()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete entry",
        variant: "destructive",
      })
    }
  }

  const handleEditSuccess = () => {
    setEditingEntry(null)
    fetchEntries()
  }

  const clearDateFilter = () => {
    setDateFilterStartDate("")
    setDateFilterEndDate("")
  }

  const exportToCSV = () => {
    const headers = ["Patient ID", "Patient Name", "Medication", "DRP Type", "Doctor Response", "Pharmacist", "Date"]
    const csvContent = [
      headers.join(","),
      ...filteredEntries.map((entry) =>
        [
          `"${entry.patient_id}"`,
          `"${entry.patient_name}"`,
          `"${entry.medication}"`,
          `"${entry.drp_type}"`,
          `"${entry.doctor_response}"`,
          `"${entry.pharmacist_signature}"`,
          `"${new Date(entry.created_at).toLocaleDateString()}"`,
        ].join(","),
      ),
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `drp-entries-${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "Success",
      description: "CSV file exported successfully",
    })
  }

  const exportToPDF = async () => {
    try {
      const printWindow = window.open("", "_blank")
      if (!printWindow) return

      const currentDate = new Date().toLocaleDateString()

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>DRP Entries Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .header { text-align: center; margin-bottom: 30px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Drug-Related Problems (DRP) Entries - ${currentDate}</h1>
          </div>
          <table>
            <thead>
              <tr>
                <th>Patient ID</th>
                <th>Patient Name</th>
                <th>Medication</th>
                <th>DRP Type</th>
                <th>Doctor Response</th>
                <th>Pharmacist</th>
                <th>Date</th>
              </tr>
            </thead>
            <tbody>
              ${filteredEntries
                .map(
                  (entry) => `
                <tr>
                  <td>${entry.patient_id}</td>
                  <td>${entry.patient_name}</td>
                  <td>${entry.medication}</td>
                  <td>${entry.drp_type}</td>
                  <td>${entry.doctor_response}</td>
                  <td>${entry.pharmacist_signature}</td>
                  <td>${new Date(entry.created_at).toLocaleDateString()}</td>
                </tr>
              `,
                )
                .join("")}
            </tbody>
          </table>
        </body>
        </html>
      `

      printWindow.document.write(htmlContent)
      printWindow.document.close()

      setTimeout(() => {
        printWindow.print()
        printWindow.close()
      }, 500)

      toast({
        title: "Success",
        description: "PDF export initiated",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export PDF",
        variant: "destructive",
      })
    }
  }

  const generateDRPReport = () => {
    if (!reportStartDate || !reportEndDate) {
      toast({
        title: "Error",
        description: "Please select both start and end dates",
        variant: "destructive",
      })
      return
    }

    if (new Date(reportStartDate) > new Date(reportEndDate)) {
      toast({
        title: "Error",
        description: "Start date cannot be after end date",
        variant: "destructive",
      })
      return
    }

    try {
      const startDate = new Date(reportStartDate)
      const endDate = new Date(reportEndDate)
      endDate.setHours(23, 59, 59, 999)

      const reportEntries = entries.filter((entry) => {
        const entryDate = new Date(entry.created_at)
        return entryDate >= startDate && entryDate <= endDate
      })

      let filteredReportEntries = reportEntries

      if (filterType !== "all") {
        filteredReportEntries = filteredReportEntries.filter((entry) => entry.drp_type === filterType)
      }

      if (filterResponse !== "all") {
        filteredReportEntries = filteredReportEntries.filter((entry) => entry.doctor_response === filterResponse)
      }

      if (filterPharmacist !== "all") {
        filteredReportEntries = filteredReportEntries.filter((entry) => entry.pharmacist_signature === filterPharmacist)
      }

      if (filteredReportEntries.length === 0) {
        toast({
          title: "No Data",
          description: "No DRP entries found for the selected period and filters",
          variant: "destructive",
        })
        return
      }

      const totalDRPs = filteredReportEntries.length
      const acceptedDRPs = filteredReportEntries.filter((entry) => entry.doctor_response === "Accepted").length
      const rejectedDRPs = filteredReportEntries.filter((entry) => entry.doctor_response === "Rejected").length
      const noResponseDRPs = filteredReportEntries.filter((entry) => entry.doctor_response === "No Response").length

      const drpTypeBreakdown = filteredReportEntries.reduce(
        (acc, entry) => {
          acc[entry.drp_type] = (acc[entry.drp_type] || 0) + 1
          return acc
        },
        {} as Record<string, number>,
      )

      const pharmacistBreakdown = filteredReportEntries.reduce(
        (acc, entry) => {
          acc[entry.pharmacist_signature] = (acc[entry.pharmacist_signature] || 0) + 1
          return acc
        },
        {} as Record<string, number>,
      )

      const reportWindow = window.open("", "_blank")
      if (!reportWindow) return

      const formatDate = (dateStr: string) => new Date(dateStr).toLocaleDateString()
      const currentDate = new Date().toLocaleDateString()
      const currentTime = new Date().toLocaleTimeString()

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>DRP Summary Report</title>
          <style>
            @page { 
              size: A4; 
              margin: 0.75in; 
            }
            body { 
              font-family: Arial, sans-serif; 
              margin: 0;
              padding: 0;
              font-size: 12px;
              line-height: 1.4;
            }
            .header { 
              text-align: center; 
              margin-bottom: 30px; 
              border-bottom: 3px solid #2563eb;
              padding-bottom: 15px;
            }
            .report-title { 
              font-size: 24px; 
              font-weight: bold; 
              color: #1e40af;
              margin: 0 0 10px 0;
            }
            .report-subtitle {
              font-size: 14px;
              color: #64748b;
              margin: 5px 0;
            }
            .report-period {
              font-size: 16px;
              font-weight: bold;
              color: #374151;
              margin: 10px 0;
            }
            .section {
              margin-bottom: 30px;
            }
            .section-title {
              font-size: 18px;
              font-weight: bold;
              color: #1e40af;
              margin-bottom: 15px;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 5px;
            }
            table { 
              width: 100%; 
              border-collapse: collapse; 
              margin-bottom: 20px;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            th { 
              background-color: #f8fafc; 
              border: 1px solid #d1d5db; 
              padding: 12px 8px; 
              text-align: left;
              font-weight: bold;
              color: #374151;
            }
            td { 
              border: 1px solid #d1d5db; 
              padding: 10px 8px; 
              text-align: left;
            }
            tr:nth-child(even) { 
              background-color: #f9fafb; 
            }
            .summary-table th {
              background-color: #dbeafe;
              color: #1e40af;
            }
            .breakdown-table th {
              background-color: #ecfdf5;
              color: #065f46;
            }
            .pharmacist-table th {
              background-color: #fef3c7;
              color: #92400e;
            }
            .number-cell {
              text-align: center;
              font-weight: bold;
            }
            .percentage {
              color: #6b7280;
              font-size: 11px;
            }
            .footer {
              margin-top: 40px;
              padding-top: 20px;
              border-top: 1px solid #e5e7eb;
              text-align: center;
              color: #6b7280;
              font-size: 10px;
            }
            .filters-applied {
              background-color: #f0f9ff;
              border: 1px solid #bae6fd;
              border-radius: 6px;
              padding: 10px;
              margin-bottom: 20px;
              font-size: 11px;
            }
            .filter-item {
              margin: 2px 0;
              color: #0369a1;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1 class="report-title">Drug-Related Problems (DRP) Summary Report</h1>
            <div class="report-subtitle">Generated on ${currentDate} at ${currentTime}</div>
            <div class="report-period">Report Period: ${formatDate(reportStartDate)} - ${formatDate(reportEndDate)}</div>
          </div>

          ${
            filterType !== "all" || filterResponse !== "all" || filterPharmacist !== "all"
              ? `
          <div class="filters-applied">
            <strong>Active Filters:</strong>
            ${filterType !== "all" ? `<div class="filter-item">• DRP Type: ${filterType}</div>` : ""}
            ${filterResponse !== "all" ? `<div class="filter-item">• Doctor Response: ${filterResponse}</div>` : ""}
            ${filterPharmacist !== "all" ? `<div class="filter-item">• Pharmacist: ${filterPharmacist}</div>` : ""}
          </div>
          `
              : ""
          }

          <div class="section">
            <h2 class="section-title">📊 DRP Summary Statistics</h2>
            <table class="summary-table">
              <thead>
                <tr>
                  <th>Metric</th>
                  <th>Count</th>
                  <th>Percentage</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>Total DRPs</strong></td>
                  <td class="number-cell">${totalDRPs}</td>
                  <td class="number-cell">100%</td>
                </tr>
                <tr>
                  <td>Accepted DRPs</td>
                  <td class="number-cell">${acceptedDRPs}</td>
                  <td class="number-cell">${totalDRPs > 0 ? ((acceptedDRPs / totalDRPs) * 100).toFixed(1) : 0}%</td>
                </tr>
                <tr>
                  <td>Rejected DRPs</td>
                  <td class="number-cell">${rejectedDRPs}</td>
                  <td class="number-cell">${totalDRPs > 0 ? ((rejectedDRPs / totalDRPs) * 100).toFixed(1) : 0}%</td>
                </tr>
                <tr>
                  <td>No Response DRPs</td>
                  <td class="number-cell">${noResponseDRPs}</td>
                  <td class="number-cell">${totalDRPs > 0 ? ((noResponseDRPs / totalDRPs) * 100).toFixed(1) : 0}%</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="section">
            <h2 class="section-title">🏥 DRP Type Breakdown</h2>
            <table class="breakdown-table">
              <thead>
                <tr>
                  <th>DRP Type</th>
                  <th>Count</th>
                  <th>Percentage</th>
                </tr>
              </thead>
              <tbody>
                ${Object.entries(drpTypeBreakdown)
                  .sort(([, a], [, b]) => b - a)
                  .map(
                    ([type, count]) => `
                    <tr>
                      <td>${type}</td>
                      <td class="number-cell">${count}</td>
                      <td class="number-cell">${totalDRPs > 0 ? ((count / totalDRPs) * 100).toFixed(1) : 0}%</td>
                    </tr>
                  `,
                  )
                  .join("")}
              </tbody>
            </table>
          </div>

          

          <div class="footer">
            <p>This report was automatically generated by the DRP Management System</p>
            <p>Report contains ${totalDRPs} DRP entries from ${formatDate(reportStartDate)} to ${formatDate(reportEndDate)}</p>
          </div>
        </body>
        </html>
      `

      reportWindow.document.write(htmlContent)
      reportWindow.document.close()

      setTimeout(() => {
        reportWindow.print()
        reportWindow.close()
      }, 1000)

      setShowReportDialog(false)

      toast({
        title: "Success",
        description: `DRP summary report generated for ${totalDRPs} entries`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate DRP report",
        variant: "destructive",
      })
    }
  }

  // Use the full list of DRP types from database for the filter dropdown
  const uniqueTypes = drpTypes.map((t) => t.name)
  const uniqueResponses = [...new Set(entries.map((entry) => entry.doctor_response))]
  // Replace hard-coded/existing pharmacist signatures with dynamic list from users table
  const uniquePharmacists = pharmacists

  if (loading) {
    return <div className="text-center py-8">Loading...</div>
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
          <CardTitle>
            DRP Entries ({filteredEntries.length} of {entries.length})
          </CardTitle>
          <div className="flex flex-col sm:flex-row gap-2 sm:items-center">
            {/* Date Filter Row */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDateFilter(!showDateFilter)}
              className="w-full sm:w-auto"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Date Filter
            </Button>

            {/* Export & Generate Row */}
            <div className="flex gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={exportToCSV}>
                    <FileText className="h-4 w-4 mr-2" />
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={exportToPDF}>
                    <FileText className="h-4 w-4 mr-2" />
                    Export as PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Dialog open={showReportDialog} onOpenChange={setShowReportDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <FileBarChart className="h-4 w-4 mr-2" />
                    Generate Report
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Generate DRP Summary Report</DialogTitle>
                    <DialogDescription>
                      Select a date range to generate a comprehensive DRP summary report with statistics and breakdowns.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="start-date" className="text-right">
                        Start Date
                      </Label>
                      <Input
                        id="start-date"
                        type="date"
                        value={reportStartDate}
                        onChange={(e) => setReportStartDate(e.target.value)}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="end-date" className="text-right">
                        End Date
                      </Label>
                      <Input
                        id="end-date"
                        type="date"
                        value={reportEndDate}
                        onChange={(e) => setReportEndDate(e.target.value)}
                        className="col-span-3"
                      />
                    </div>
                    {(filterType !== "all" || filterResponse !== "all" || filterPharmacist !== "all") && (
                      <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm">
                        <p className="font-medium text-blue-800 mb-1">Active Filters Will Be Applied:</p>
                        {filterType !== "all" && <p className="text-blue-700">• DRP Type: {filterType}</p>}
                        {filterResponse !== "all" && <p className="text-blue-700">• Doctor Response: {filterResponse}</p>}
                        {filterPharmacist !== "all" && <p className="text-blue-700">• Pharmacist: {filterPharmacist}</p>}
                      </div>
                    )}
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setShowReportDialog(false)}>
                      Cancel
                    </Button>
                    <Button type="button" onClick={generateDRPReport}>
                      Generate Report
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>

        {/* Date Range Filter */}
        {showDateFilter && (
          <div className="bg-gray-50 border rounded-lg p-4 mt-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Label htmlFor="date-filter-start" className="text-sm font-medium">
                  From:
                </Label>
                <Input
                  id="date-filter-start"
                  type="date"
                  value={dateFilterStartDate}
                  onChange={(e) => setDateFilterStartDate(e.target.value)}
                  className="w-40"
                />
              </div>
              <div className="flex items-center gap-2">
                <Label htmlFor="date-filter-end" className="text-sm font-medium">
                  To:
                </Label>
                <Input
                  id="date-filter-end"
                  type="date"
                  value={dateFilterEndDate}
                  onChange={(e) => setDateFilterEndDate(e.target.value)}
                  className="w-40"
                />
              </div>
              <Button variant="outline" size="sm" onClick={clearDateFilter}>
                Clear
              </Button>
            </div>
            {(dateFilterStartDate || dateFilterEndDate) && (
              <p className="text-sm text-gray-600 mt-2">
                {dateFilterStartDate && dateFilterEndDate
                  ? `Showing entries from ${new Date(dateFilterStartDate).toLocaleDateString()} to ${new Date(dateFilterEndDate).toLocaleDateString()}`
                  : dateFilterStartDate
                    ? `Showing entries from ${new Date(dateFilterStartDate).toLocaleDateString()} onwards`
                    : `Showing entries up to ${new Date(dateFilterEndDate).toLocaleDateString()}`}
              </p>
            )}
          </div>
        )}

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by Patient ID, Name, or Medication..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex gap-2">
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {uniqueTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filterResponse} onValueChange={setFilterResponse}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by Response" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Responses</SelectItem>
                {uniqueResponses.map((response) => (
                  <SelectItem key={response} value={response}>
                    {response}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filterPharmacist} onValueChange={setFilterPharmacist}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by Pharmacist" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Pharmacists</SelectItem>
                {uniquePharmacists.map((pharmacist) => (
                  <SelectItem key={pharmacist} value={pharmacist}>
                    {pharmacist}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="">Patient ID</TableHead>
                <TableHead className="hidden md:table-cell">Patient Name</TableHead>
                <TableHead className="hidden md:table-cell">Medication</TableHead>
                <TableHead>DRP Type</TableHead>
                <TableHead className="hidden md:table-cell">Doctor Response</TableHead>
                <TableHead className="hidden md:table-cell">Pharmacist</TableHead>
                <TableHead className="hidden md:table-cell">Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEntries.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                    No DRP entries found
                  </TableCell>
                </TableRow>
              ) : (
                filteredEntries.map((entry) => (
                  <TableRow key={entry.id}>
                    <TableCell className="font-medium">{entry.patient_id}</TableCell>
                    <TableCell className="hidden md:table-cell">{entry.patient_name}</TableCell>
                    <TableCell className="hidden md:table-cell">{entry.medication}</TableCell>
                    <TableCell>{entry.drp_type}</TableCell>
                    <TableCell className="hidden md:table-cell">
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          entry.doctor_response === "Accepted"
                            ? "bg-green-100 text-green-800"
                            : entry.doctor_response === "Rejected"
                              ? "bg-red-100 text-red-800"
                              : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {entry.doctor_response}
                      </span>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">{entry.pharmacist_signature}</TableCell>
                    <TableCell className="hidden md:table-cell">{new Date(entry.created_at).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => setViewingEntry(entry)}>
                          <Eye className="h-4 w-4" />
                        </Button>

                        {session?.user && (session.user.role === "admin" || (session.user as any).username === entry.username) && (
                          <>
                            <Button variant="outline" size="sm" onClick={() => setEditingEntry(entry)}>
                              <Edit className="h-4 w-4" />
                            </Button>

                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="outline" size="sm">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action cannot be undone. This will permanently delete the DRP entry.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => handleDelete(entry.id)}>Delete</AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {filteredEntries.length > 0 && (
          <div className="mt-4 text-sm text-muted-foreground">
            Showing {filteredEntries.length} of {entries.length} entries
          </div>
        )}
      </CardContent>

      {editingEntry && (
        <EditDrpModal entry={editingEntry} onClose={() => setEditingEntry(null)} onSuccess={handleEditSuccess} />
      )}

      {viewingEntry && (
        <Dialog open={!!viewingEntry} onOpenChange={(open) => !open && setViewingEntry(null)}>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle>View DRP Entry</DialogTitle>
              <DialogDescription>Full details of the DRP entry.</DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Patient ID</Label>
                  <p className="border rounded-md p-2 bg-muted">{viewingEntry?.patient_id}</p>
                </div>
                <div className="space-y-2">
                  <Label>Patient Name</Label>
                  <p className="border rounded-md p-2 bg-muted">{viewingEntry?.patient_name}</p>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label>Medication</Label>
                  <p className="border rounded-md p-2 bg-muted">{viewingEntry?.medication}</p>
                </div>

                <div className="space-y-2">
                  <Label>DRP Type</Label>
                  <p className="border rounded-md p-2 bg-muted">{viewingEntry?.drp_type}</p>
                </div>
                <div className="space-y-2">
                  <Label>Doctor Response</Label>
                  <p className="border rounded-md p-2 bg-muted">{viewingEntry?.doctor_response}</p>
                </div>

                <div className="space-y-2">
                  <Label>Pharmacist Signature</Label>
                  <p className="border rounded-md p-2 bg-muted">{viewingEntry?.pharmacist_signature}</p>
                </div>
                <div className="space-y-2">
                  <Label>Date</Label>
                  <p className="border rounded-md p-2 bg-muted">
                    {viewingEntry ? new Date(viewingEntry.created_at).toLocaleString() : ""}
                  </p>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button onClick={() => setViewingEntry(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  )
}
