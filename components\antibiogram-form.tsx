"use client"

import type React from "react"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { CalendarIcon, Save, RotateCcw, AlertTriangle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { supabase } from "@/lib/supabase"

// Types for our database tables
interface Antibiogram {
  id?: string
  patient_id: string
  patient_name: string
  unit: string
  date: string
  resistance_type?: string
  culture_type: string
  microorganism_id?: string
  microorganism_name?: string
  microorganism_type?: string
  created_at?: string
  updated_at?: string
}

interface AntibioticResult {
  id?: string
  antibiogram_id: string
  antibiotic_name: string
  antibiotic_class?: string
  result: string
  created_at?: string
}

interface AntibiogramFormProps {
  onSubmitSuccess: () => void
}

const microorganisms = [
  { id: "GP001", name: "Staph (MSSA)", type: "Gram-positive" },
  { id: "GP002", name: "MRSA", type: "Gram-positive" },
  { id: "GP003", name: "Streptococcus", type: "Gram-positive" },
  { id: "GP004", name: "Enterococcus", type: "Gram-positive" },
  { id: "GP005", name: "CONS", type: "Gram-positive" },
  { id: "GN001", name: "Klebsiella", type: "Gram-negative" },
  { id: "GN002", name: "E-Coli", type: "Gram-negative" },
  { id: "GN003", name: "Pseudomonas", type: "Gram-negative" },
  { id: "GN004", name: "Proteus", type: "Gram-negative" },
  { id: "GN005", name: "Acinetobacter", type: "Gram-negative" },
  { id: "GN006", name: "Enterobacter", type: "Gram-negative" },
]

const antibiotics = [
  { name: "Amikin", class: "Aminoglycosides" },
  { name: "Augmentin", class: "Penicillins" },
  { name: "Azetronem", class: "Monobactams" },
  { name: "Cipro", class: "Fluoroquinolones" },
  { name: "Claforan", class: "Cephalosporins" },
  { name: "Colistin", class: "Polymyxins" },
  { name: "Dalacin", class: "Lincosamides" },
  { name: "Doxycycline", class: "Tetracyclines" },
  { name: "Fosfomycin", class: "Phosphonic acids" },
  { name: "Fortum", class: "Cephalosporins" },
  { name: "Gentamycin", class: "Aminoglycosides" },
  { name: "Inzanz", class: "Carbapenems" },
  { name: "Maxipeme", class: "Cephalosporins" },
  { name: "Meropenem", class: "Carbapenems" },
  { name: "Moxiflox", class: "Fluoroquinolones" },
  { name: "Rocephen", class: "Cephalosporins" },
  { name: "Septrin", class: "Folate inhibitors" },
  { name: "Sulperazone", class: "Cephalosporins" },
  { name: "Targocid", class: "Glycopeptides" },
  { name: "Tavanic", class: "Fluoroquinolones" },
  { name: "Tazocin", class: "Penicillins" },
  { name: "Tienam", class: "Carbapenems" },
  { name: "Tygacil", class: "Glycylcyclines" },
  { name: "Unasyn", class: "Penicillins" },
  { name: "Uvamin", class: "Nitrofurans" },
  { name: "Vancomycin", class: "Glycopeptides" },
  { name: "Zavicefta", class: "Cephalosporins" },
  { name: "Zithromax", class: "Macrolides" },
  { name: "Zyvox", class: "Oxazolidinones" },
].sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()))

const brandToGeneric: Record<string, string> = {
  Amikin: "Amikacin",
  Augmentin: "Amoxicillin/Clavulanic a",
  Azetronem: "Azetronem",
  Cipro: "Ciprofloxacin",
  Claforan: "Cefotaxime",
  Colistin: "Colistin",
  Dalacin: "Clindamycin",
  Doxycycline: "Doxycycline",
  Fosfomycin: "Fosfomycin",
  Fortum: "Ceftazidime",
  Gentamycin: "Gentamycin",
  Inzanz: "Ertapenem",
  Maxipeme: "Cefipime",
  Meropenem: "Meropenem",
  Moxiflox: "Moxifloxacin",
  Rocephen: "Ceftriaxone",
  Septrin: "Trimethoprin/Sulfamethoxazole",
  Sulperazone: "Cefoperazone/Sulbactam",
  Targocid: "Teicoplanin",
  Tavanic: "Levofloxacin",
  Tazocin: "Pipracillin/Tazobactam",
  Tienam: "Imipenem",
  Tygacil: "Tigecycline",
  Unasyn: "Ampicillin/Sulbactam",
  Uvamin: "Nitrofurantoin",
  Vancomycin: "Vancomycin",
  Zavicefta: "Ceftazidime/Avibactam",
  Zithromax: "Azithromycin",
  Zyvox: "Linezolid",
}

const unitOptions = ["ICU", "IMC", "InPatient"]

const cultureTypes = ["Blood", "Sputum", "Urine", "Wound"]
const resistanceTypes = [
  { value: "MDR", label: "MDR (Multi-Drug Resistant)" },
  { value: "XDR", label: "XDR (Extensively Drug-Resistant)" },
  { value: "Pan-DR", label: "Pan-DR (Pan-Drug Resistant)" },
  { value: "Not Tested", label: "None" },
]

export function AntibiogramForm({ onSubmitSuccess }: AntibiogramFormProps) {
  const [formData, setFormData] = useState({
    date: "",
    patientId: "",
    patientName: "",
    unit: "",
    resistanceType: "",
    cultureType: "",
    microorganism: "",
    antibiotics: {} as Record<string, string>,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [dateError, setDateError] = useState<string | null>(null)

  const isValidDate = (dateStr: string): boolean => {
    const regex = /^(\d{2})\/(\d{2})\/(\d{4})$/
    const match = dateStr.match(regex)
    if (!match) return false

    const day = parseInt(match[1], 10)
    const month = parseInt(match[2], 10)
    const year = parseInt(match[3], 10)

    if (month < 1 || month > 12) return false

    const daysInMonth = new Date(year, month, 0).getDate()
    return day >= 1 && day <= daysInMonth
  }

  const convertToISO = (dateStr: string): string => {
    const [day, month, year] = dateStr.split("/")
    return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`
  }

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value
    const digits = input.replace(/\D/g, "")

    let formatted = ""
    if (digits.length > 0) formatted += digits.substring(0, 2)
    if (digits.length >= 3) formatted += "/" + digits.substring(2, 4)
    if (digits.length >= 5) formatted += "/" + digits.substring(4, 8)

    setFormData((prev) => ({ ...prev, date: formatted }))

    if (formatted.length === 10 && !isValidDate(formatted)) {
      setDateError("Please enter a valid date (DD/MM/YYYY).")
    } else {
      setDateError(null)
    }
  }

  const handleAntibioticChange = (antibiotic: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      antibiotics: {
        ...prev.antibiotics,
        [antibiotic]: value,
      },
    }))
  }

  const checkForDuplicates = async (patientId: string, cultureType: string, date: string) => {
    const { data: existingEntries, error } = await supabase
      .from("antibiograms")
      .select("id, date")
      .eq("patient_id", patientId)
      .eq("culture_type", cultureType)

    if (error) {
      throw new Error(`Error checking for duplicates: ${error.message}`)
    }

    if (existingEntries && existingEntries.length > 0) {
      const newDate = new Date(date)

      for (const entry of existingEntries) {
        const existingDate = new Date(entry.date)

        if (newDate.getTime() === existingDate.getTime()) {
          throw new Error(
            `Duplicate entry found: An antibiogram for patient ${patientId} with culture type ${cultureType} already exists for this date.`,
          )
        }

        if (newDate > existingDate) {
          throw new Error(
            `A more recent entry already exists for patient ${patientId} with culture type ${cultureType}. Only older entries can replace existing ones.`,
          )
        }

        if (newDate < existingDate) {
          const { error: deleteError } = await supabase.from("antibiograms").delete().eq("id", entry.id)

          if (deleteError) {
            throw new Error(`Error replacing existing entry: ${deleteError.message}`)
          }
        }
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)
    setSuccess(null)

    try {
      if (
        !formData.date ||
        !formData.patientId ||
        !formData.unit ||
        !formData.cultureType ||
        !formData.microorganism
      ) {
        throw new Error("Please fill in all required fields.")
      }

      if (!isValidDate(formData.date)) {
        throw new Error("Please enter the date in DD/MM/YYYY format.")
      }

      const isoDate = convertToISO(formData.date)

      await checkForDuplicates(formData.patientId, formData.cultureType, isoDate)

      const selectedOrganism = microorganisms.find((org) => org.id === formData.microorganism)

      const antibiogramData: Omit<Antibiogram, "id" | "created_at" | "updated_at"> = {
        patient_id: formData.patientId,
        patient_name: formData.patientName,
        unit: formData.unit,
        date: isoDate,
        resistance_type:
          formData.resistanceType && formData.resistanceType !== "Not Tested"
            ? formData.resistanceType
            : undefined,
        culture_type: formData.cultureType,
        microorganism_id: selectedOrganism?.id || undefined,
        microorganism_name: selectedOrganism?.name || undefined,
        microorganism_type: selectedOrganism?.type || undefined,
      }

      const { data: antibiogramResult, error: antibiogramError } = await supabase
        .from("antibiograms")
        .insert([antibiogramData])
        .select()
        .single()

      if (antibiogramError) {
        throw new Error(`Error saving antibiogram: ${antibiogramError.message}`)
      }

      const antibioticResults: Omit<AntibioticResult, "id" | "created_at">[] = []

      for (const antibiotic of antibiotics) {
        const result = formData.antibiotics[antibiotic.name] || "None"
        antibioticResults.push({
          antibiogram_id: antibiogramResult.id,
          antibiotic_name: antibiotic.name,
          antibiotic_class: antibiotic.class,
          result: result,
        })
      }

      const { error: resultsError } = await supabase.from("antibiotic_results").insert(antibioticResults)

      if (resultsError) {
        throw new Error(`Error saving antibiotic results: ${resultsError.message}`)
      }

      setSuccess("Antibiogram data saved successfully!")
      onSubmitSuccess()

      setTimeout(() => {
        handleReset()
        setSuccess(null)
      }, 3000)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleReset = () => {
    setFormData({
      date: "",
      patientId: "",
      patientName: "",
      unit: "",
      resistanceType: "",
      cultureType: "",
      microorganism: "",
      antibiotics: {},
    })
    setError(null)
    setSuccess(null)
    setDateError(null)
  }

  const selectedOrganism = microorganisms.find((org) => org.id === formData.microorganism)

  const sortedAntibiotics = useMemo(() => {
    return [...antibiotics].sort((a, b) => {
      const genA = brandToGeneric[a.name] || a.name
      const genB = brandToGeneric[b.name] || b.name
      return genA.toLowerCase().localeCompare(genB.toLowerCase())
    })
  }, [])

  return (
    <Card>
      

      <CardContent className="p-6">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-6 border-green-200 bg-green-50">
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Patient Information Section */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="text"
                inputMode="numeric"
                placeholder="DD/MM/YYYY"
                value={formData.date}
                onChange={handleDateChange}
                required
              />
              {dateError && <p className="text-xs text-red-600 mt-1">{dateError}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="patientId">Patient ID *</Label>
              <Input
                id="patientId"
                placeholder="Enter patient ID"
                value={formData.patientId}
                onChange={(e) => setFormData((prev) => ({ ...prev, patientId: e.target.value }))}
                inputMode="numeric"
                pattern="[0-9]*"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="patientName">Patient Name</Label>
              <Input
                id="patientName"
                placeholder="Enter patient full name"
                value={formData.patientName}
                onChange={(e) => setFormData((prev) => ({ ...prev, patientName: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Unit *</Label>
              <Select
                value={formData.unit}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, unit: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select unit" />
                </SelectTrigger>
                <SelectContent>
                  {unitOptions.map((unit) => (
                    <SelectItem key={unit} value={unit}>
                      {unit}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* Clinical Information Section - Reorganized to horizontal layout */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Culture Type */}
            <div className="space-y-2">
              <Label>Culture Type *</Label>
              <Select
                value={formData.cultureType}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, cultureType: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select culture type" />
                </SelectTrigger>
                <SelectContent>
                  {cultureTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Microorganism */}
            <div className="space-y-2">
              <Label>Microorganism *</Label>
              <Select
                value={formData.microorganism}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, microorganism: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select microorganism" />
                </SelectTrigger>
                <SelectContent>
                  <div className="px-2 py-1 text-xs font-semibold text-green-600 bg-green-50">
                    Gram-positive Bacteria
                  </div>
                  {microorganisms
                    .filter((org) => org.type === "Gram-positive")
                    .map((org) => (
                      <SelectItem key={org.id} value={org.id}>
                        <div className="flex justify-between items-center w-full">
                          <span>{org.name}</span>
                          <span className="text-xs text-gray-500 ml-2">({org.id})</span>
                        </div>
                      </SelectItem>
                    ))}
                  <div className="px-2 py-1 text-xs font-semibold text-red-600 bg-red-50">Gram-negative Bacteria</div>
                  {microorganisms
                    .filter((org) => org.type === "Gram-negative")
                    .map((org) => (
                      <SelectItem key={org.id} value={org.id}>
                        <div className="flex justify-between items-center w-full">
                          <span>{org.name}</span>
                          <span className="text-xs text-gray-500 ml-2">({org.id})</span>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              {selectedOrganism && (
                <p className="text-sm text-gray-600">
                  Selected: <span className="font-medium">{selectedOrganism.name}</span>
                  <span
                    className={`ml-2 px-2 py-1 rounded text-xs ${
                      selectedOrganism.type === "Gram-positive" ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                    }`}
                  >
                    {selectedOrganism.type}
                  </span>
                </p>
              )}
            </div>

            {/* Resistance Type */}
            <div className="space-y-2">
              <Label>Resistance Type *</Label>
              <Select
                value={formData.resistanceType}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, resistanceType: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select resistance type" />
                </SelectTrigger>
                <SelectContent>
                  {resistanceTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* Antibiotic Susceptibility Section */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Antibiotic Susceptibility Testing</h3>
              <p className="text-sm text-gray-600 mb-4">
                Select the susceptibility result for each antibiotic tested
              </p>
            </div>

            <div className="grid grid-cols-3 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-1.5 sm:gap-4">
              {sortedAntibiotics.map((antibiotic) => {
                // Hide the antibiotic row once the user has made any selection (Sensitive, Resistant, or None)
                if (formData.antibiotics[antibiotic.name] !== undefined) {
                  return null
                }
                const genericName = brandToGeneric[antibiotic.name] || antibiotic.name
                return (
                  <div
                    key={antibiotic.name}
                    className="p-1.5 sm:p-3 border rounded-lg hover:bg-gray-50 space-y-1 sm:space-y-2"
                  >
                    <div className="text-center mb-1 sm:mb-2">
                      <div className="font-medium text-xxs sm:text-base truncate">
                        {genericName}
                      </div>
                      <div className="text-xxs sm:text-xs text-gray-500 truncate">
                        {antibiotic.name}
                      </div>
                    </div>
                    <div className="flex flex-col space-y-1 sm:flex-row sm:justify-center sm:items-center sm:space-y-0 sm:space-x-2">
                      <label className="flex items-center space-x-1 cursor-pointer">
                        <input
                          type="radio"
                          name={antibiotic.name}
                          value="Sensitive"
                          checked={formData.antibiotics[antibiotic.name] === "Sensitive"}
                          onChange={(e) => handleAntibioticChange(antibiotic.name, e.target.value)}
                          className="text-blue-600 scale-75"
                        />
                        <span className="text-xxs sm:text-xs px-1 py-0.5 rounded bg-green-100 text-green-700">Sensitive</span>
                      </label>
                      <label className="flex items-center space-x-1 cursor-pointer">
                        <input
                          type="radio"
                          name={antibiotic.name}
                          value="Resistant"
                          checked={formData.antibiotics[antibiotic.name] === "Resistant"}
                          onChange={(e) => handleAntibioticChange(antibiotic.name, e.target.value)}
                          className="text-blue-600 scale-75"
                        />
                        <span className="text-xxs sm:text-xs px-1 py-0.5 rounded bg-red-100 text-red-700">Resistant</span>
                      </label>
                      <label className="flex items-center space-x-1 cursor-pointer">
                        <input
                          type="radio"
                          name={antibiotic.name}
                          value="None"
                          checked={
                            formData.antibiotics[antibiotic.name] === "None" || formData.antibiotics[antibiotic.name] === undefined
                          }
                          onChange={(e) => handleAntibioticChange(antibiotic.name, e.target.value)}
                          className="text-blue-600 scale-75"
                        />
                        <span className="text-xxs sm:text-xs px-1 py-0.5 rounded bg-gray-100 text-gray-700">None</span>
                      </label>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              className="flex items-center gap-2"
              disabled={isSubmitting}
            >
              <RotateCcw className="h-4 w-4" />
              Reset Form
            </Button>
            <Button
              type="submit"
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
              disabled={isSubmitting}
            >
              <Save className="h-4 w-4" />
              {isSubmitting ? "Saving..." : "Save Antibiogram"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
} 







