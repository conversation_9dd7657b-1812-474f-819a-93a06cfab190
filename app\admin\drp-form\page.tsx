'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from '@/hooks/use-toast'
import { Edit, Trash2 } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import type { DrpType } from '@/lib/supabase'

export default function AdminDrpTypeManager() {
  const [drpTypes, setDrpTypes] = useState<DrpType[]>([])
  const [newTypeName, setNewTypeName] = useState('')
  const [loading, setLoading] = useState(true)
  const [editingType, setEditingType] = useState<DrpType | null>(null)
  const [editingName, setEditingName] = useState('')

  // Fetch on mount
  useEffect(() => {
    fetchTypes()
  }, [])

  const fetchTypes = async () => {
    setLoading(true)
    try {
      const res = await fetch('/api/drp-types')
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to fetch types')
      setDrpTypes(data as DrpType[])
    } catch (err: any) {
      toast({ title: 'Error', description: err.message, variant: 'destructive' })
    } finally {
      setLoading(false)
    }
  }

  const handleAddType = async () => {
    if (!newTypeName.trim()) return
    try {
      const res = await fetch('/api/drp-types', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newTypeName.trim() }),
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to add type')
      toast({ title: 'Success', description: 'DRP type added successfully' })
      setNewTypeName('')
      fetchTypes()
    } catch (err: any) {
      toast({ title: 'Error', description: err.message, variant: 'destructive' })
    }
  }

  const handleUpdateType = async () => {
    if (!editingType) return
    try {
      const res = await fetch(`/api/drp-types/${editingType.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: editingName.trim() }),
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to update type')
      toast({ title: 'Success', description: 'DRP type updated successfully' })
      setEditingType(null)
      fetchTypes()
    } catch (err: any) {
      toast({ title: 'Error', description: err.message, variant: 'destructive' })
    }
  }

  const handleDeleteType = async (id: number) => {
    try {
      const res = await fetch(`/api/drp-types/${id}`, { method: 'DELETE' })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to delete type')
      toast({ title: 'Success', description: 'DRP type deleted successfully' })
      fetchTypes()
    } catch (err: any) {
      toast({ title: 'Error', description: err.message, variant: 'destructive' })
    }
  }

  return (
    <Card className="mx-auto w-full max-w-3xl">
      <CardHeader>
        <CardTitle>Manage DRP Types</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add new type */}
        <div className="flex gap-2">
          <Input
            placeholder="New DRP type name"
            value={newTypeName}
            onChange={(e) => setNewTypeName(e.target.value)}
            className="flex-1"
          />
          <Button onClick={handleAddType}>Add</Button>
        </div>

        {/* Types table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead className="w-32">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={2} className="text-center py-4">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : drpTypes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={2} className="text-center py-4 text-muted-foreground">
                    No DRP types found.
                  </TableCell>
                </TableRow>
              ) : (
                drpTypes.map((type) => (
                  <TableRow key={type.id}>
                    <TableCell>{type.name}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {/* Edit */}
                        <Dialog onOpenChange={(open) => !open && setEditingType(null)} open={editingType?.id === type.id}>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setEditingType(type)
                                setEditingName(type.name)
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Edit DRP type</DialogTitle>
                              <DialogDescription>Update the name of the DRP type.</DialogDescription>
                            </DialogHeader>

                            <Input value={editingName} onChange={(e) => setEditingName(e.target.value)} />

                            <DialogFooter>
                              <Button onClick={handleUpdateType} disabled={!editingName.trim()}>
                                Save
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>

                        {/* Delete */}
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the DRP type.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDeleteType(type.id)}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
} 