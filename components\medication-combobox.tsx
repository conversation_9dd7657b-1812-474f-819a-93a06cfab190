"use client"

import { useState } from "react"
import { Check, ChevronDown } from "lucide-react"
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import type { Medication } from "@/lib/supabase"

interface MedicationComboboxProps {
  value: string
  onChange: (val: string) => void
  options: Medication[]
  error?: boolean
}

export default function MedicationCombobox({
  value,
  onChange,
  options,
  error,
}: MedicationComboboxProps) {
  const [open, setOpen] = useState(false)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn("w-full justify-between", error && "border-red-500")}
          role="combobox"
          aria-expanded={open}
        >
          {value || "Select medication"}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>

      <PopoverContent className="p-0 w-60">
        <Command>
          <CommandInput placeholder="Search medications…" autoFocus />
          <CommandList>
            <CommandEmpty>No medication found.</CommandEmpty>
            {options.map((med) => (
              <CommandItem
                key={med.id}
                value={med.name}
                onSelect={(val) => {
                  onChange(val)
                  setOpen(false)
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    med.name === value ? "opacity-100" : "opacity-0",
                  )}
                />
                {med.name}
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
} 