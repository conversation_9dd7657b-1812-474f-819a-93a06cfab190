-- Migration: Add username column to drp_entries and dic_entries
-- Run this SQL in your Supabase project (SQL editor or psql)

-- DRP entries
alter table if exists drp_entries
  add column if not exists username text not null default '';

-- DIC entries
alter table if exists dic_entries
  add column if not exists username text not null default '';

-- Optional: create index to speed up owner queries
create index if not exists drp_entries_username_idx on drp_entries(username);
create index if not exists dic_entries_username_idx on dic_entries(username); 