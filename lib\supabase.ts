import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseKey)

export type DrpEntry = {
  id: number
  patient_id: string
  patient_name: string
  medication: string
  drp_type: string
  doctor_response: string
  pharmacist_signature: string
  username: string
  created_at: string
  updated_at: string
}

export type Medication = {
  id: number
  name: string
  created_at: string
}

// New types for the Antibiogram module
export type Organism = {
  id: number
  name: string
  type: string | null
  created_at: string
  updated_at: string
}

export type Antibiotic = {
  id: number
  name: string
  class: string | null
  created_at: string
  updated_at: string
}

export type AntibiogramResult = {
  id: number
  organism_id: number
  antibiotic_id: number
  susceptibility: string
  mic: string | null
  culture_type: string | null
  tested_on: string
  pharmacist_signature: string
  created_at: string
  updated_at: string
}

export type DrpType = {
  id: number
  name: string
  created_at: string
}
