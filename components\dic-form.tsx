"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { supabase } from "@/lib/supabase"
import { toast } from "@/hooks/use-toast"

interface DicFormProps {
  onSubmitSuccess: () => void
}

export function DicForm({ onSubmitSuccess }: DicFormProps) {
  const { data: session } = useSession()
  const [formData, setFormData] = useState({
    question: "",
    answer: "",
    inquirer: "",
    pharmacistSignature: session?.user?.name ?? "",
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Keep pharmacistSignature in sync with session
  useEffect(() => {
    if (session?.user?.name) {
      setFormData((prev) => ({ ...prev, pharmacistSignature: session.user.name }))
    }
  }, [session])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.question.trim()) {
      newErrors.question = "Question is required"
    }

    if (!formData.answer.trim()) {
      newErrors.answer = "Answer is required"
    }

    if (!formData.inquirer.trim()) {
      newErrors.inquirer = "Inquirer role is required"
    }

    // pharmacistSignature auto-filled

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isFormValid = () => {
    return (
      formData.question.trim() !== "" &&
      formData.answer.trim() !== "" &&
      formData.inquirer.trim() !== ""
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      const { error } = await supabase.from("dic_entries").insert([
        {
          question: formData.question,
          answer: formData.answer,
          inquirer: formData.inquirer,
          pharmacist_signature: formData.pharmacistSignature,
          username: (session?.user as any)?.username,
        },
      ])

      if (error) throw error

      setFormData({
        question: "",
        answer: "",
        inquirer: "",
        pharmacistSignature: session?.user?.name ?? "",
      })
      setErrors({})

      toast({
        title: "Success",
        description: "DIC Entry Submitted",
      })

      onSubmitSuccess()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to submit DIC entry",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full mx-auto">
      <CardHeader>
        <CardTitle>Submit Drug Information Inquiry</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="question">Question</Label>
            <Textarea
              id="question"
              placeholder="Enter the drug information question"
              value={formData.question}
              onChange={(e) => setFormData((prev) => ({ ...prev, question: e.target.value }))}
              className={errors.question ? "border-red-500" : ""}
              rows={4}
            />
            {errors.question && <p className="text-sm text-red-500">{errors.question}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="answer">Answer</Label>
            <Textarea
              id="answer"
              placeholder="Enter the answer to the question"
              value={formData.answer}
              onChange={(e) => setFormData((prev) => ({ ...prev, answer: e.target.value }))}
              className={errors.answer ? "border-red-500" : ""}
              rows={6}
            />
            {errors.answer && <p className="text-sm text-red-500">{errors.answer}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="inquirer">Inquirer</Label>
              <Select
                value={formData.inquirer}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, inquirer: value }))
                }
              >
                <SelectTrigger
                  id="inquirer"
                  className={errors.inquirer ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select inquirer role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Pharmacist">Pharmacist</SelectItem>
                  <SelectItem value="Nurse">Nurse</SelectItem>
                  <SelectItem value="Doctor">Doctor</SelectItem>
                </SelectContent>
              </Select>
              {errors.inquirer && <p className="text-sm text-red-500">{errors.inquirer}</p>}
            </div>

            <div className="space-y-2">
              <Label>Pharmacist</Label>
              <Input value={session?.user?.name ?? ""} readOnly />
            </div>
          </div>

          <Button type="submit" className="w-full" disabled={!isFormValid() || isSubmitting}>
            {isSubmitting ? "Submitting..." : "Submit DIC Entry"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
