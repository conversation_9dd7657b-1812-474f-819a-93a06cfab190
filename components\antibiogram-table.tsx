"use client"

import { useEffect, useState, useRef } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Edit, Trash2, ChevronLeft, ChevronRight, Loader2 } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { EditAntibiogramModal } from "@/components/edit-antibiogram-modal"
import { supabase } from "@/lib/supabase"
import { toast } from "@/hooks/use-toast"
import { exportAntibiogramsToExcel, parseAntibiogramExcel } from "@/lib/excel-antibiogram"
import { useIsMobile } from "@/hooks/use-mobile"

interface Antibiogram {
  id: string
  patient_id: string
  patient_name: string | null
  unit: string
  date: string
  resistance_type: string | null
  culture_type: string
  microorganism_name: string | null
  microorganism_id: string | null
  microorganism_type: string | null
}

interface AntibiogramTableProps {
  refreshTrigger: number
}

export function AntibiogramTable({ refreshTrigger }: AntibiogramTableProps) {
  const [entries, setEntries] = useState<Antibiogram[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [editingEntry, setEditingEntry] = useState<Antibiogram | null>(null)
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const isMobile = useIsMobile()
  const pageSize = 10

  // derived pages
  const totalPages = Math.ceil(entries.length / pageSize)
  const paginatedEntries = entries.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  // Computed helpers for selection
  const visibleIds = entries.slice((currentPage - 1) * pageSize, currentPage * pageSize).map((e) => e.id)
  const allVisibleSelected = visibleIds.length > 0 && visibleIds.every((id) => selectedIds.includes(id))
  const someVisibleSelected = !allVisibleSelected && visibleIds.some((id) => selectedIds.includes(id))

  useEffect(() => {
    fetchEntries()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshTrigger])

  const fetchEntries = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("antibiograms")
        .select("id, patient_id, patient_name, unit, date, resistance_type, culture_type, microorganism_name, microorganism_id, microorganism_type")
        .order("date", { ascending: false })

      if (error) throw error
      setEntries((data as Antibiogram[]) || [])
    } catch (error: any) {
      toast({ title: "Error", description: error.message || "Failed to load antibiograms", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase.from("antibiograms").delete().eq("id", id)
      if (error) throw error
      toast({ title: "Deleted", description: "Antibiogram deleted successfully." })
      fetchEntries()
    } catch (error: any) {
      toast({ title: "Error", description: error.message || "Failed to delete entry", variant: "destructive" })
    }
  }

  const nextPage = () => setCurrentPage((p) => Math.min(p + 1, totalPages))
  const prevPage = () => setCurrentPage((p) => Math.max(p - 1, 1))

  const toggleSelect = (id: string | "all") => {
    if (id === "all") {
      setSelectedIds((prev) => {
        if (allVisibleSelected) {
          // Deselect all visible
          return prev.filter((pid) => !visibleIds.includes(pid))
        }
        // Select all visible (add missing)
        const newSet = new Set([...prev, ...visibleIds])
        return Array.from(newSet)
      })
      return
    }

    setSelectedIds((prev) => {
      if (prev.includes(id)) {
        return prev.filter((pid) => pid !== id)
      }
      return [...prev, id]
    })
  }

  const handleBulkDelete = async () => {
    if (selectedIds.length === 0) return
    try {
      setLoading(true)
      const { error } = await supabase.from("antibiograms").delete().in("id", selectedIds)
      if (error) throw error

      toast({ title: "Deleted", description: `${selectedIds.length} antibiogram(s) deleted successfully.` })

      // Remove from local state
      setEntries((prev) => prev.filter((e) => !selectedIds.includes(e.id)))
      setSelectedIds([])
    } catch (error: any) {
      toast({ title: "Error", description: error.message || "Failed to delete selected entries", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  // Mapping from antibiotic full name (as stored in DB) to column code used in Excel export
  const antibioticNameToCode: Record<string, string> = {
    Amikin: "AMK",
    Augmentin: "AUG",
    Azetronem: "AZT",
    Cipro: "CIP",
    Claforan: "CLF",
    Colistin: "COL",
    Dalacin: "DLC",
    Doxycycline: "DOX",
    Fosfomycin: "FOS",
    Fortum: "FOR",
    Gentamycin: "GEN",
    Inzanz: "INV",
    Maxipeme: "MAX",
    Meropenem: "MER",
    Moxiflox: "MOX",
    Rocephen: "ROC",
    Septrin: "SEP",
    Sulperazone: "SUL",
    Targocid: "TRG",
    Tavanic: "TAV",
    Tazocin: "TAZ",
    Tienam: "TIN",
    Tygacil: "TYG",
    Unasyn: "UNS",
    Uvamin: "UVM",
    Vancomycin: "VAN",
    Zavicefta: "ZAV",
    Zithromax: "ZIT",
    Zyvox: "ZYV",
  }

  const handleExport = async () => {
    try {
      setLoading(true)

      // Fetch antibiograms along with their antibiotic results
      const { data, error } = await supabase
        .from("antibiograms")
        .select(
          `id, date, patient_id, patient_name, unit, culture_type, resistance_type, microorganism_name, microorganism_id, microorganism_type, antibiotic_results(antibiotic_name, result)`,
        )

      if (error) throw error

      if (!data || data.length === 0) {
        toast({ title: "Nothing to export", description: "There are no antibiogram records to export." })
        return
      }

      // Transform data into CleanAntibiogram[] expected by helper
      const transformed = data.map((row: any) => {
        const obj: any = {
          patient_id: row.patient_id,
          patient_name: row.patient_name ?? "",
          unit: row.unit,
          date: row.date,
          resistance_type:
            row.resistance_type && row.resistance_type.toLowerCase() !== "not tested"
              ? row.resistance_type
              : "",
          culture_type: row.culture_type,
          microorganism_name: row.microorganism_name ?? "",
          microorganism_id: row.microorganism_id ?? "",
          microorganism_type: row.microorganism_type ?? "",
        }

        // Ensure all antibiotic columns exist initialized to empty string
        Object.values(antibioticNameToCode).forEach((code) => (obj[code] = ""))

        // Populate results
        if (row.antibiotic_results && Array.isArray(row.antibiotic_results)) {
          row.antibiotic_results.forEach((res: any) => {
            const code = antibioticNameToCode[res.antibiotic_name as string]
            if (code) {
              let val = res.result as string | null | undefined
              if (!val || val.toLowerCase() === "not tested" || val.toLowerCase() === "none") {
                val = ""
              } else if (val.toLowerCase() === "resistant") {
                val = "R"
              } else if (val.toLowerCase() === "sensitive") {
                val = "S"
              }
              obj[code] = val
            }
          })
        }

        return obj
      })

      exportAntibiogramsToExcel(transformed)
    } catch (err: any) {
      toast({ title: "Export failed", description: err?.message || "Unable to export data", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    try {
      setLoading(true)

      // Parse the Excel file into clean objects (includes antibiotic code columns)
      const cleanRows = await parseAntibiogramExcel(file)

      // Build mapping code -> full name (inverse of antibioticNameToCode)
      const codeToName: Record<string, string> = Object.fromEntries(
        Object.entries(antibioticNameToCode).map(([name, code]) => [code, name]),
      )

      // We will first collect core rows (without id) and keep a mapping so that
      // we can attach antibiotic result rows **after** the upsert returns the
      // definitive IDs. This avoids conflicts with the database-level unique
      // constraint on (patient_id,culture_type,date) and allows us to safely
      // update existing rows.

      const antibiogramRows: any[] = []
      // key => antibiotic results temporary store
      const resultsByKey: Record<string, any[]> = {}

      cleanRows.forEach((row) => {
        const {
          patient_id,
          patient_name,
          unit,
          date,
          resistance_type,
          culture_type,
          microorganism_name,
          microorganism_id,
          microorganism_type,
          ...rest
        } = row as any

        // Build a key matching the DB unique constraint so that we can map
        // back to the correct row once the upsert completes.
        const dateKey = new Date(date).toISOString().slice(0, 10) // YYYY-MM-DD
        const rowKey = `${patient_id}|${culture_type}|${dateKey}`

        // Prepare and push core row (omit antibiotic code columns & id – let DB decide).
        antibiogramRows.push({
          patient_id,
          patient_name,
          unit,
          date,
          resistance_type,
          culture_type,
          microorganism_name,
          microorganism_id,
          microorganism_type,
        })

        // Collect antibiotic_results rows but store them temporarily keyed by rowKey.
        Object.entries(rest).forEach(([code, value]) => {
          if (!(code in codeToName)) return

          const resultVal = (value ?? "None").toString().trim()

          if (!resultsByKey[rowKey]) resultsByKey[rowKey] = []

          resultsByKey[rowKey].push({
            // antibiogram_id to be filled later once we know the real ID
            antibiotic_name: codeToName[code],
            antibiotic_class: null,
            result: resultVal || "None",
          })
        })
      })

      // Upsert antibiograms first
      const { data: savedRows, error: abError } = await supabase
        .from("antibiograms")
        .upsert(antibiogramRows, { onConflict: "patient_id,culture_type,date" })
        .select()

      if (abError) throw abError

      // Using the savedRows, build the final antibiotic_results array.
      const antibioticResultRows: any[] = []

      savedRows.forEach((row: any) => {
        const dateKey = new Date(row.date).toISOString().slice(0, 10)
        const key = `${row.patient_id}|${row.culture_type}|${dateKey}`

        const results = resultsByKey[key]
        if (!results || results.length === 0) return

        // For a clean replace, wipe any existing results for this antibiogram.
        antibioticResultRows.push(
          ...results.map((res) => ({
            ...res,
            antibiogram_id: row.id,
          }))
        )
      })

      // Delete existing antibiotic_results for the affected antibiograms
      const idsToRefresh = savedRows.map((r: any) => r.id)
      if (idsToRefresh.length) {
        await supabase.from("antibiotic_results").delete().in("antibiogram_id", idsToRefresh)
      }

      // Bulk-insert the fresh antibiotic_results rows
      const chunkSize = 500
      for (let i = 0; i < antibioticResultRows.length; i += chunkSize) {
        const chunk = antibioticResultRows.slice(i, i + chunkSize)
        if (chunk.length) {
          const { error: resErr } = await supabase.from("antibiotic_results").insert(chunk)
          if (resErr) throw resErr
        }
      }

      toast({ title: "Import successful", description: `${antibiogramRows.length} records processed.` })
      fetchEntries()
    } catch (err: any) {
      const message = err?.message || "Failed to import data."
      toast({ title: "Import failed", description: message, variant: "destructive" })
    } finally {
      setLoading(false)
      // reset file input so the same file can be chosen again if needed
      e.target.value = ""
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between flex-wrap gap-2">
        <CardTitle>Antibiogram Entries</CardTitle>
        <div className="flex flex-wrap gap-2">
          {selectedIds.length > 0 && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size={isMobile ? "sm" : "default"}>
                  {isMobile ? `Delete (${selectedIds.length})` : `Delete Selected (${selectedIds.length})`}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Selected Entries</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete {selectedIds.length} selected antibiogram{selectedIds.length > 1 ? "s" : ""}? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleBulkDelete}>Delete</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
          <Button variant="outline" size={isMobile ? "sm" : "default"} onClick={handleExport}>
            {isMobile ? "Export" : "Export to Excel"}
          </Button>
          <Button variant="outline" size={isMobile ? "sm" : "default"} onClick={() => fileInputRef.current?.click()}>
            {isMobile ? "Import" : "Import from Excel"}
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            accept=".xlsx"
            className="hidden"
            onChange={handleFileSelect}
          />
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-10">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        ) : entries.length === 0 ? (
          <p className="text-center py-6 text-sm text-muted-foreground">No entries found.</p>
        ) : (
          <div className="space-y-4">
            <div className={`overflow-x-auto border rounded-md ${isMobile ? 'max-w-[calc(100vw-2rem)]' : ''}`}>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-10">
                      <Checkbox
                        checked={allVisibleSelected ? true : someVisibleSelected ? "indeterminate" : false}
                        onCheckedChange={() => toggleSelect("all")}
                      />
                    </TableHead>
                    <TableHead>Patient ID</TableHead>
                    {!isMobile && <TableHead>Patient Name</TableHead>}
                    {!isMobile && <TableHead>Date</TableHead>}
                    {!isMobile && <TableHead>Unit</TableHead>}
                    {!isMobile && <TableHead>Culture Type</TableHead>}
                    <TableHead>Microorganism</TableHead>
                    <TableHead>Resistance</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedEntries.map((entry) => {
                    const isSelected = selectedIds.includes(entry.id)
                    return (
                      <TableRow key={entry.id} className={isSelected ? "bg-accent/30" : undefined}>
                        <TableCell className="w-10">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleSelect(entry.id)}
                          />
                        </TableCell>
                        <TableCell>{entry.patient_id}</TableCell>
                        {!isMobile && <TableCell>{entry.patient_name || "-"}</TableCell>}
                        {!isMobile && <TableCell>{new Date(entry.date).toLocaleDateString()}</TableCell>}
                        {!isMobile && <TableCell>{entry.unit}</TableCell>}
                        {!isMobile && <TableCell>{entry.culture_type}</TableCell>}
                        <TableCell>{entry.microorganism_name || "-"}</TableCell>
                        <TableCell>{entry.resistance_type || "-"}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            {/* Edit button placeholder for future implementation */}
                            <Button size={isMobile ? "sm" : "icon"} variant="ghost" onClick={() => setEditingEntry(entry)}>
                              <Edit className="h-4 w-4" />
                              {isMobile && <span className="ml-2">Edit</span>}
                            </Button>
                            {/* Delete */}
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button size={isMobile ? "sm" : "icon"} variant="ghost">
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                  {isMobile && <span className="ml-2">Delete</span>}
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Entry</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete this antibiogram? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => handleDelete(entry.id)}>Delete</AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex justify-end items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className="h-8 w-8"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm">
                  {isMobile ? `${currentPage}/${totalPages}` : `Page ${currentPage} of ${totalPages}`}
                </span>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}

            {editingEntry && (
              <EditAntibiogramModal
                entry={editingEntry}
                onClose={() => setEditingEntry(null)}
                onSuccess={fetchEntries}
              />
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 