"use client"

import { useState, useEffect } from "react"
import { DicForm } from "@/components/dic-form"
import { DicTable } from "@/components/dic-table"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Toaster } from "@/components/ui/toaster"

export default function DicPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleFormSubmitSuccess = () => {
    setRefreshTrigger((prev) => prev + 1)
  }

  // Set page title for accessibility
  useEffect(() => {
    document.title = "Drug Information Center - Clinical Dashboard"
  }, [])

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900">Drug Information Center</h1>
        <p className="text-gray-600 mt-2">Manage and track drug information inquiries and responses</p>
      </div>

      <Tabs defaultValue="submit" className="w-full md:w-[60vw] mx-auto">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="submit">Submit DIC Form</TabsTrigger>
          <TabsTrigger value="entries">View DIC Entries</TabsTrigger>
        </TabsList>

        <TabsContent value="submit" className="mt-6 w-full mx-auto">
          <DicForm onSubmitSuccess={handleFormSubmitSuccess} />
        </TabsContent>

        <TabsContent value="entries" className="mt-6 w-full mx-auto">
          <DicTable refreshTrigger={refreshTrigger} />
        </TabsContent>
      </Tabs>

      <Toaster />
    </div>
  )
}
