import { type DrpEntry } from "@/lib/supabase"

/*
 Utility functions for Drug-Related Problem (DRP) exports & summary report generation.
 Keeping this logic out of the React component drastically reduces its size and
 lets us unit-test each helper in complete isolation.
*/

// ----------------------------- CSV ----------------------------------------
export function buildDrpCSV(entries: DrpEntry[]): string {
  const headers = [
    "Patient ID",
    "Patient Name",
    "Medication",
    "DRP Type",
    "Doctor Response",
    "Pharmacist",
    "Date",
  ]

  const body = entries.map((e) =>
    [
      e.patient_id,
      e.patient_name,
      e.medication,
      e.drp_type,
      e.doctor_response,
      e.pharmacist_signature,
      new Date(e.created_at).toLocaleDateString(),
    ]
      .map((cell) => `"${cell}"`)
      .join(","),
  )

  return [headers.join(","), ...body].join("\n")
}

// ---------------------- Plain entries PDF ---------------------------------
export function buildDrpEntriesPDFHTML(entries: DrpEntry[]): string {
  const currentDate = new Date().toLocaleDateString()
  const rows = entries
    .map(
      (e) => `
        <tr>
          <td>${e.patient_id}</td>
          <td>${e.patient_name}</td>
          <td>${e.medication}</td>
          <td>${e.drp_type}</td>
          <td>${e.doctor_response}</td>
          <td>${e.pharmacist_signature}</td>
          <td>${new Date(e.created_at).toLocaleDateString()}</td>
        </tr>`,
    )
    .join("")

  return `<!DOCTYPE html>
<html>
<head>
  <title>DRP Entries Report</title>
  <style>
    body{font-family:Arial, sans-serif;margin:20px;}
    h1{text-align:center;margin-bottom:20px;}
    table{width:100%;border-collapse:collapse;margin-top:20px;}
    th,td{border:1px solid #ddd;padding:8px;text-align:left;}
    th{background:#f2f2f2;font-weight:bold;}
    tr:nth-child(even){background:#f9f9f9;}
  </style>
</head>
<body>
  <h1>Drug-Related Problems (DRP) Entries – ${currentDate}</h1>
  <table>
    <thead>
      <tr>
        <th>Patient ID</th><th>Patient Name</th><th>Medication</th>
        <th>DRP Type</th><th>Doctor Response</th><th>Pharmacist</th><th>Date</th>
      </tr>
    </thead>
    <tbody>${rows}</tbody>
  </table>
</body>
</html>`
}

// -------------------------- Summary report PDF ----------------------------
interface SummaryOptions {
  entries: DrpEntry[]
  startDate: string
  endDate: string
  filterType: string
  filterResponse: string
  filterPharmacist: string
}

export function buildDrpSummaryHTML({
  entries,
  startDate,
  endDate,
  filterType,
  filterResponse,
  filterPharmacist,
}: SummaryOptions): { html: string; totalDRPs: number } {
  // --- filter data by period ---------------------------------------------
  const s = new Date(startDate)
  const e = new Date(endDate)
  e.setHours(23, 59, 59, 999)

  let data = entries.filter((x) => {
    const d = new Date(x.created_at)
    return d >= s && d <= e
  })
  if (filterType !== "all") data = data.filter((x) => x.drp_type === filterType)
  if (filterResponse !== "all") data = data.filter((x) => x.doctor_response === filterResponse)
  if (filterPharmacist !== "all") data = data.filter((x) => x.pharmacist_signature === filterPharmacist)

  // --- statistics ---------------------------------------------------------
  const total = data.length
  const accepted = data.filter((x) => x.doctor_response === "Accepted").length
  const rejected = data.filter((x) => x.doctor_response === "Rejected").length
  const noResp = data.filter((x) => x.doctor_response === "No Response").length

  const byType: Record<string, number> = {}
  const byPharmacist: Record<string, number> = {}
  data.forEach((x) => {
    byType[x.drp_type] = (byType[x.drp_type] ?? 0) + 1
    byPharmacist[x.pharmacist_signature] = (byPharmacist[x.pharmacist_signature] ?? 0) + 1
  })

  // --- helpers -----------------------------------------------------------
  const fmt = (d: string | Date) => new Date(d).toLocaleDateString()
  const pct = (n: number) => (total ? ((n / total) * 100).toFixed(1) : "0")

  // --- html --------------------------------------------------------------
  const html = `<!DOCTYPE html><html><head><title>DRP Summary Report</title>
<style>
  @page{size:A4;margin:0.75in;}
  body{font-family:Arial, sans-serif;font-size:12px;line-height:1.4;margin:0;}
  .header{text-align:center;margin-bottom:30px;border-bottom:3px solid #2563eb;padding-bottom:15px;}
  .header h1{font-size:24px;color:#1e40af;margin:0;}
  .header .subtitle{color:#64748b;}
  .header .period{font-weight:bold;color:#374151;margin-top:5px;}
  table{width:100%;border-collapse:collapse;margin:18px 0;}
  th,td{border:1px solid #d1d5db;padding:8px;text-align:left;}
  th{background:#f8fafc;font-weight:bold;color:#374151;}
  tr:nth-child(even){background:#f9fafb;}
  .number{text-align:center;font-weight:bold;}
  .section-title{font-size:18px;font-weight:bold;color:#1e40af;margin:25px 0 10px;}
  .filters{background:#f0f9ff;border:1px solid #bae6fd;border-radius:6px;padding:10px;font-size:11px;}
  .footer{margin-top:40px;border-top:1px solid #e5e7eb;text-align:center;color:#6b7280;font-size:10px;padding-top:10px;}
</style>
</head><body>
  <div class="header">
    <h1>Drug-Related Problems (DRP) Summary Report</h1>
    <div class="subtitle">Generated on ${fmt(new Date())} – ${new Date().toLocaleTimeString()}</div>
    <div class="period">${fmt(s)} – ${fmt(e)}</div>
  </div>
  ${
    filterType !== "all" || filterResponse !== "all" || filterPharmacist !== "all"
      ? `<div class="filters"><strong>Active Filters:</strong>
        ${filterType !== "all" ? `<div>• DRP Type: ${filterType}</div>` : ""}
        ${filterResponse !== "all" ? `<div>• Doctor Response: ${filterResponse}</div>` : ""}
        ${filterPharmacist !== "all" ? `<div>• Pharmacist: ${filterPharmacist}</div>` : ""}
      </div>`
      : ""
  }
  <div class="section-title">📊 Summary Statistics</div>
  <table>
    <tr><th>Metric</th><th>Count</th><th>%</th></tr>
    <tr><td><strong>Total DRPs</strong></td><td class="number">${total}</td><td class="number">100</td></tr>
    <tr><td>Accepted</td><td class="number">${accepted}</td><td class="number">${pct(accepted)}</td></tr>
    <tr><td>Rejected</td><td class="number">${rejected}</td><td class="number">${pct(rejected)}</td></tr>
    <tr><td>No Response</td><td class="number">${noResp}</td><td class="number">${pct(noResp)}</td></tr>
  </table>
  <div class="section-title">🏥 DRP Type Breakdown</div>
  <table>
    <tr><th>Type</th><th>Count</th><th>%</th></tr>
    ${Object.entries(byType)
      .sort(([,a],[,b]) => b - a)
      .map(([t,c]) => `<tr><td>${t}</td><td class="number">${c}</td><td class="number">${pct(c)}</td></tr>`)
      .join("")}
  </table>
  <div class="footer">Report contains ${total} DRP entries<br/>${fmt(s)} – ${fmt(e)} • DRP Management System</div>
</body></html>`

  return { html, totalDRPs: total }
} 