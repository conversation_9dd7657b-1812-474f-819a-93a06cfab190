"use client"

import { <PERSON>, CardContent } from "@/components/ui/card"
import Link from "next/link"
import { <PERSON>ll, BookO<PERSON>, Shield } from "lucide-react"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="container max-w-md">
        <div className="text-center mb-10">
          <h1 className="text-4xl font-bold text-foreground mb-2">Clinical Pharmacy Portal</h1>
        </div>

        <div className="grid gap-6">
          <Link href="/drp" passHref>
            <Card className="hover:shadow-lg transition-shadow duration-300 cursor-pointer">
              <CardContent className="flex items-center p-6">
                <div className="bg-primary/10 p-3 rounded-full mr-4">
                  <Pill className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold">DRP Management System</h2>
                  <p className="text-muted-foreground">Track and manage drug-related problems</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/dic" passHref>
            <Card className="hover:shadow-lg transition-shadow duration-300 cursor-pointer">
              <CardContent className="flex items-center p-6">
                <div className="bg-primary/10 p-3 rounded-full mr-4">
                  <BookOpen className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold">Drug Information Center</h2>
                  <p className="text-muted-foreground">Manage drug information inquiries</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/antibiogram" passHref>
            <Card className="hover:shadow-lg transition-shadow duration-300 cursor-pointer">
              <CardContent className="flex items-center p-6">
                <div className="bg-primary/10 p-3 rounded-full mr-4">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold">Antibiogram Management</h2>
                  <p className="text-muted-foreground">Monitor antimicrobial resistance patterns</p>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}
