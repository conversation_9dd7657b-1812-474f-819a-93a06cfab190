"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

export function AdminNav() {
  const pathname = usePathname()
  return (
    <nav className="flex flex-col gap-1">
      <Link
        href="/admin"
        className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
          pathname === "/admin"
            ? "bg-accent text-accent-foreground"
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
        }`}
      >
        Edit Users
      </Link>
      <Link
        href="/admin/drp-form"
        className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
          pathname === "/admin/drp-form"
            ? "bg-accent text-accent-foreground"
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
        }`}
      >
        Edit DRP Form
      </Link>
    </nav>
  )
} 