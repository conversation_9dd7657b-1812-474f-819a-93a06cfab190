"use client"

import { useState } from "react"
import { DrpForm } from "@/components/drp-form"
import { DrpTable } from "@/components/drp-table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"

export default function DrpPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleSubmitSuccess = () => {
    setRefreshTrigger((prev) => prev + 1)
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900">Drug-Related Problems (DRP) Management</h1>
        <p className="text-gray-600 mt-2">Submit and manage drug-related problem reports</p>
      </div>

      <Tabs defaultValue="submit" className="w-full md:w-[60vw] mx-auto">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="submit">Submit DRP Entry</TabsTrigger>
          <TabsTrigger value="entries">View DRP Entries</TabsTrigger>
        </TabsList>

        <TabsContent value="submit" className="mt-6 w-full mx-auto">
          <DrpForm onSubmitSuccess={handleSubmitSuccess} />
        </TabsContent>

        <TabsContent value="entries" className="mt-6 w-full mx-auto">
          <DrpTable refreshTrigger={refreshTrigger} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
