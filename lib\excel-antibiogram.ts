import * as XLSX from "xlsx"
import { saveAs } from "file-saver"
import { v4 as uuidv4 } from "uuid"

// Mapping for antibiotic susceptibility codes to full descriptor
const SUSCEPTIBILITY_MAP: Record<string, string> = {
  r: "Resistant",
  resistant: "Resistant",
  s: "Sensitive",
  sensitive: "Sensitive",
  "": "None",
  none: "None",
  null: "None",
  "not tested": "None",
}

// All antibiotic column codes used in the spreadsheet / DB
const ANTIBIOTIC_COLS = [
  "AUG",
  "UNS",
  "TAZ",
  "CLF",
  "ROC",
  "FOR",
  "SUL",
  "MAX",
  "ZAV",
  "MER",
  "TIN",
  "INV",
  "AZT",
  "CIP",
  "TAV",
  "MOX",
  "GEN",
  "AMK",
  "VAN",
  "ZYV",
  "TRG",
  "DOX",
  "ZIT",
  "DLC",
  "TYG",
  "COL",
  "UVM",
  "FOS",
  "SEP",
] as const

// Microorganism name -> {id, type}
const MICROORGANISM_MAP: Record<string, { id: string; type: "Gram-positive" | "Gram-negative" }> = {
  "staph (mssa)": { id: "GP001", type: "Gram-positive" },
  mrsa: { id: "GP002", type: "Gram-positive" },
  streptococcus: { id: "GP003", type: "Gram-positive" },
  enterococcus: { id: "GP004", type: "Gram-positive" },
  cons: { id: "GP005", type: "Gram-positive" },
  klebsiella: { id: "GN001", type: "Gram-negative" },
  "e-coli": { id: "GN002", type: "Gram-negative" },
  "e. coli": { id: "GN002", type: "Gram-negative" },
  "e coli": { id: "GN002", type: "Gram-negative" },
  pseudomonas: { id: "GN003", type: "Gram-negative" },
  proteus: { id: "GN004", type: "Gram-negative" },
  acinetobacter: { id: "GN005", type: "Gram-negative" },
  enterobacter: { id: "GN006", type: "Gram-negative" },
}

// Allowed culture types mapping
const CULTURE_TYPE_MAP: Record<string, "Blood" | "Sputum" | "Urine" | "Wound"> = {
  blood: "Blood",
  sputum: "Sputum",
  urine: "Urine",
  wound: "Wound",
}

// Helper to map susceptibility value to DB compatible value
function normalizeSusceptibility(raw: any): string {
  if (raw === null || raw === undefined) return "None"
  const val = String(raw).trim().toLowerCase()
  return SUSCEPTIBILITY_MAP[val] ?? "None"
}

// Helper to validate UUID v4 string
const uuidV4Regex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

// ------------------------
// Helper to parse date values (supports Excel serial numbers)
function parseExcelDate(value: any): Date | null {
  if (value === null || value === undefined || value === "") return null

  // If numeric, treat as Excel serial date (days since 1899-12-31)
  if (typeof value === "number") {
    // Excel incorrectly treats 1900 as leap year; adding offset of 25569 converts to Unix epoch days
    const jsTime = (value - 25569) * 86400 * 1000
    return new Date(jsTime)
  }

  // If the value is a string that represents a number, attempt numeric parse first
  const maybeNum = Number(value)
  if (!Number.isNaN(maybeNum)) {
    const jsTime = (maybeNum - 25569) * 86400 * 1000
    return new Date(jsTime)
  }

  // Fallback to normal Date parsing
  let d: Date;
  try {
    // Try parsing as ISO string first
    d = new Date(value)
    // Check if valid
    if (!Number.isNaN(d.getTime())) {
      return d
    }
    
    // Try parsing as MM/DD/YYYY format
    if (typeof value === "string" && value.indexOf('/') > -1) {
      const parts = value.split('/')
      if (parts.length === 3) {
        const month = parseInt(parts[0]) - 1 // months are 0-indexed in JS
        const day = parseInt(parts[1])
        const year = parseInt(parts[2])
        d = new Date(year, month, day)
        if (!Number.isNaN(d.getTime())) {
          return d
        }
      }
    }
  } catch (e) {
    console.error("Date parse error:", e)
  }
  
  // If we get here, date parsing failed
  return null
}

// Minimal representation used for importing/exporting. All fields are optional except the required ones enforced in validation.
export interface CleanAntibiogram {
  id?: string
  patient_id: string
  patient_name?: string | null
  unit: string
  date: string // ISO or any parseable date string
  resistance_type?: string | null
  culture_type: string
  microorganism_name?: string | null
  microorganism_id?: string | null
  microorganism_type?: string | null
  // Antibiotic susceptibility columns (optional)
  AUG?: string | number | null
  UNS?: string | number | null
  TAZ?: string | number | null
  CLF?: string | number | null
  ROC?: string | number | null
  FOR?: string | number | null
  SUL?: string | number | null
  MAX?: string | number | null
  ZAV?: string | number | null
  MER?: string | number | null
  TIN?: string | number | null
  INV?: string | number | null
  AZT?: string | number | null
  CIP?: string | number | null
  TAV?: string | number | null
  MOX?: string | number | null
  GEN?: string | number | null
  AMK?: string | number | null
  VAN?: string | number | null
  ZYV?: string | number | null
  TRG?: string | number | null
  DOX?: string | number | null
  ZIT?: string | number | null
  DLC?: string | number | null
  TYG?: string | number | null
  COL?: string | number | null
  UVM?: string | number | null
  FOS?: string | number | null
  SEP?: string | number | null
  [key: string]: any // allow additional dynamic keys
}

/**
 * Triggers a browser download of the provided rows as an .xlsx file.
 */
export function exportAntibiogramsToExcel(rows: CleanAntibiogram[]) {
  // Antibiotic columns in required sequence per export specification
  const antibioticCols = [
    "AUG", // Augmentin
    "UNS", // Unasyn
    "TAZ", // Tazocin
    "CLF", // Claforan
    "ROC", // Rocephen
    "FOR", // Fortum
    "SUL", // Sulperazone
    "MAX", // Maxipeme
    "ZAV", // Zavicefta
    "MER", // Meropenem
    "TIN", // Tienam
    "INV", // Inzanz
    "AZT", // Azetronem
    "CIP", // Cipro
    "TAV", // Tavanic
    "MOX", // Moxiflox
    "GEN", // Gentamycin
    "AMK", // Amikin
    "VAN", // Vancomycin
    "ZYV", // Zyvox
    "TRG", // Targocid
    "DOX", // Doxycycline
    "ZIT", // Zithromax
    "DLC", // Dalacin
    "TYG", // Tygacil
    "COL", // Colistin
    "UVM", // Uvamin
    "FOS", // Fosfomycin
    "SEP", // Septrin
  ] as const

  const header = [
    "date", // formatted MM/DD/YYYY
    "patient_id",
    "patient_name",
    "unit",
    "culture_type",
    "microorganism_name",
    "resistance_type",
    ...antibioticCols,
    "microorganism_id",
    "microorganism_type",
  ]

  // Convert rows to plain objects with values in the exact header order
  const formattedRows = rows.map((row) => {
    const d = new Date(row.date)
    const mm = String(d.getMonth() + 1).padStart(2, "0")
    const dd = String(d.getDate()).padStart(2, "0")
    const yyyy = d.getFullYear()
    const dateFormatted = `${mm}/${dd}/${yyyy}`

    const obj: Record<string, any> = {
      date: dateFormatted,
      patient_id: row.patient_id,
      patient_name: row.patient_name ?? "",
      unit: row.unit,
      culture_type: row.culture_type,
      microorganism_name: row.microorganism_name ?? "",
      resistance_type: row.resistance_type ?? "",
    }

    // Append all antibiotic columns in order, default empty string if missing
    antibioticCols.forEach((col) => {
      obj[col] = row[col] ?? ""
    })

    obj["microorganism_id"] = row.microorganism_id ?? ""
    obj["microorganism_type"] = row.microorganism_type ?? ""

    return obj
  })

  const worksheet = XLSX.utils.json_to_sheet(formattedRows, { header })
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, "Antibiograms")

  const buffer = XLSX.write(workbook, { type: "array", bookType: "xlsx" })
  saveAs(new Blob([buffer]), "antibiograms.xlsx")
}

/**
 * Parses an uploaded Excel file, validates required fields, and returns a clean array suitable for upsert.
 * Throws an Error containing a newline-separated list when any validation fails.
 */
export async function parseAntibiogramExcel(file: File): Promise<CleanAntibiogram[]> {
  const data = await file.arrayBuffer()
  const workbook = XLSX.read(data)
  if (workbook.SheetNames.length === 0) {
    throw new Error("The Excel file contains no sheets.")
  }

  const worksheet = workbook.Sheets[workbook.SheetNames[0]]
  const json: Record<string, any>[] = XLSX.utils.sheet_to_json(worksheet, { defval: null })

  const required = ["patient_id", "unit", "date", "culture_type"] as const
  const errors: string[] = []

  const cleaned: CleanAntibiogram[] = json.map((row, rowIndex) => {
    const excelRow = rowIndex + 2 // account for header row being 1
    // Check required columns
    required.forEach((col) => {
      if (!row[col]) {
        errors.push(`Row ${excelRow}: ${col} is required`)
      }
    })

    // Validate date
    const parsedDate = parseExcelDate(row.date)
    if (!parsedDate) {
      errors.push(`Row ${excelRow}: invalid date`)
    }

    // ------------------------
    // Handle UUID generation/validation
    let id: string | undefined = row.id || undefined
    if (!id || !uuidV4Regex.test(id)) {
      id = uuidv4()
    }

    // ------------------------
    // Microorganism mapping
    let microorganismName: string | null = row.microorganism_name ?? null
    let microorganismId: string | null = row.microorganism_id ?? null
    let microorganismType: string | null = row.microorganism_type ?? null

    if (!microorganismId && microorganismName) {
      const key = microorganismName.toString().trim().toLowerCase()
      const mapped = MICROORGANISM_MAP[key]
      if (mapped) {
        microorganismId = mapped.id
        microorganismType = mapped.type
      }
    }

    // If type still missing but id available, derive from id prefix
    if (!microorganismType && microorganismId) {
      microorganismType = microorganismId.startsWith("GP") ? "Gram-positive" : microorganismId.startsWith("GN") ? "Gram-negative" : null
    }

    // ------------------------
    // Build cleaned record
    // Normalise culture type
    const cultureKey = (row.culture_type ?? "").toString().trim().toLowerCase()
    const normalisedCulture = CULTURE_TYPE_MAP[cultureKey]
    if (!normalisedCulture) {
      errors.push(`Row ${excelRow}: unsupported culture_type '${row.culture_type}'`)
    }

    const record: CleanAntibiogram = {
      id,
      patient_id: row.patient_id?.toString() ?? "",
      patient_name: row.patient_name ?? null,
      unit: row.unit?.toString() ?? "",
      date:
        parsedDate
          ? new Date(Date.UTC(parsedDate.getUTCFullYear(), parsedDate.getUTCMonth(), parsedDate.getUTCDate())).toISOString()
          : "",
      resistance_type: row.resistance_type ?? null,
      culture_type: normalisedCulture ?? row.culture_type?.toString() ?? "",
      microorganism_name: microorganismName,
      microorganism_id: microorganismId,
      microorganism_type: microorganismType,
    }

    // ------------------------
    // Map antibiotic susceptibility columns
    ANTIBIOTIC_COLS.forEach((code) => {
      if (code in row) {
        record[code] = normalizeSusceptibility(row[code])
      }
    })

    return record
  })

  if (errors.length) {
    throw new Error(errors.join("\n"))
  }

  // ------------------------------------------
  // Create a lookup map for detecting duplicates that would violate the DB constraint
  // idx_antibiograms_unique_patient_culture_date relies on patient_id, culture_type, and date
  const uniqueKeys = new Map<string, CleanAntibiogram>();
  
  cleaned.forEach(record => {
    // Get date in YYYY-MM-DD format for comparison
    const dateKey = record.date.slice(0, 10);
    
    // Create unique key matching the database constraint
    const key = `${record.patient_id}|${dateKey}|${record.culture_type}`;
    
    // If we've seen this key before (potential duplicate)
    if (uniqueKeys.has(key)) {
      const existingRecord = uniqueKeys.get(key)!;
      
      // Check if microorganisms are different
      const existingMicroKey = existingRecord.microorganism_id || 
        (existingRecord.microorganism_name?.toString().trim().toLowerCase() || '');
      const currentMicroKey = record.microorganism_id || 
        (record.microorganism_name?.toString().trim().toLowerCase() || '');
      
      // If microorganism differs, we need to modify the ID to avoid the constraint
      if (existingMicroKey !== currentMicroKey && currentMicroKey !== '') {
        // Generate a different ID for this record to ensure it doesn't conflict
        record.id = uuidv4();
      } else {
        // For true duplicates (same patient, date, culture, AND microorganism)
        // Skip this record entirely by not adding it to uniqueKeys
        return;
      }
    }
    
    // Store the record in our map
    uniqueKeys.set(key, record);
  });
  
  // Return only the unique records
  return Array.from(uniqueKeys.values());
} 