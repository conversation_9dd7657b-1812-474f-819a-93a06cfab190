"use client"

import { useEffect, useState } from "react"
import { supabase } from "@/lib/supabase"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { toast } from "@/hooks/use-toast"
import bcrypt from "bcryptjs"
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import { Pencil, Trash2 } from "lucide-react"
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog"

interface User {
  id: string
  name: string
  username: string
  role: string
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [form, setForm] = useState({ name: "", username: "", password: "" })
  const [loading, setLoading] = useState(false)

  // Edit user state
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [editForm, setEditForm] = useState({ name: "", username: "", password: "" })
  const [editLoading, setEditLoading] = useState(false)

  const fetchUsers = async () => {
    const { data, error } = await supabase.from("users").select("id, name, username, role")
    if (error) {
      toast({ title: "Error", description: error.message, variant: "destructive" })
      return
    }
    setUsers(data || [])
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    try {
      if (!form.name || !form.username || !form.password) {
        toast({ title: "Validation", description: "All fields are required", variant: "destructive" })
        return
      }
      const password_hash = await bcrypt.hash(form.password, 10)
      const { error } = await supabase
        .from("users")
        .insert({ name: form.name, username: form.username, password_hash, role: "user" })

      if (error) throw error
      toast({ title: "Success", description: "User created" })
      setForm({ name: "", username: "", password: "" })
      fetchUsers()
    } catch (err: any) {
      toast({ title: "Error", description: err.message, variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    const user = users.find((u) => u.id === id)
    if (user?.role === "admin") {
      toast({ title: "Action blocked", description: "Admin user cannot be deleted", variant: "destructive" })
      return
    }
    const { error } = await supabase.from("users").delete().eq("id", id)
    if (error) {
      toast({ title: "Error", description: error.message, variant: "destructive" })
    } else {
      toast({ title: "Deleted" })
      fetchUsers()
    }
  }

  const openEdit = (u: User) => {
    setEditingUser(u)
    setEditForm({ name: u.name, username: u.username, password: "" })
  }

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingUser) return
    setEditLoading(true)
    try {
      if (!editForm.name || !editForm.username) {
        toast({ title: "Validation", description: "Name and username are required", variant: "destructive" })
        return
      }

      const updatePayload: Record<string, any> = {
        name: editForm.name,
        username: editForm.username,
      }

      if (editForm.password) {
        updatePayload.password_hash = await bcrypt.hash(editForm.password, 10)
      }

      const { error } = await supabase.from("users").update(updatePayload).eq("id", editingUser.id)

      if (error) throw error

      toast({ title: "Updated", description: "User information updated" })
      setEditingUser(null)
      fetchUsers()
    } catch (err: any) {
      toast({ title: "Error", description: err.message, variant: "destructive" })
    } finally {
      setEditLoading(false)
    }
  }

  return (
    <div className="space-y-6 max-w-md mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Register New User</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              placeholder="Name"
              value={form.name}
              onChange={(e) => setForm((f) => ({ ...f, name: e.target.value }))}
              required
            />
            <Input
              placeholder="Username"
              value={form.username}
              onChange={(e) => setForm((f) => ({ ...f, username: e.target.value }))}
              required
            />
            <Input
              type="password"
              placeholder="Password"
              value={form.password}
              onChange={(e) => setForm((f) => ({ ...f, password: e.target.value }))}
              required
            />
            <Button type="submit" disabled={loading}>
              {loading ? "Saving..." : "Add User"}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {users.map((u) => (
              <li key={u.id} className="flex justify-between items-center gap-4 border p-2 rounded-md">
                <span>
                  {u.name}
                </span>
                <div className="flex gap-2">
                  <Dialog onOpenChange={(open) => !open && setEditingUser(null)}>
                    <DialogTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={() => openEdit(u)}>
                        <Pencil className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                      </DialogHeader>
                      <form onSubmit={handleEditSubmit} className="space-y-4">
                        <Input
                          placeholder="Name"
                          value={editForm.name}
                          onChange={(e) => setEditForm((f) => ({ ...f, name: e.target.value }))}
                          required
                        />
                        <Input
                          placeholder="Username"
                          value={editForm.username}
                          onChange={(e) => setEditForm((f) => ({ ...f, username: e.target.value }))}
                          required
                        />
                        <Input
                          type="password"
                          placeholder="New Password (leave blank to keep)"
                          value={editForm.password}
                          onChange={(e) => setEditForm((f) => ({ ...f, password: e.target.value }))}
                        />
                        <DialogFooter>
                          <Button type="submit" disabled={editLoading}>
                            {editLoading ? "Saving..." : "Save Changes"}
                          </Button>
                          <DialogClose asChild>
                            <Button type="button" variant="ghost">
                              Cancel
                            </Button>
                          </DialogClose>
                        </DialogFooter>
                      </form>
                    </DialogContent>
                  </Dialog>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-destructive hover:bg-destructive/10"
                        disabled={u.role === "admin"}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this user? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          onClick={() => handleDelete(u.id)}
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  )
} 